﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.RecurringPayment
{
    public class RecurringPaymentCommandHandler(IMapper mapper, IPaymentService paymentService)
        : ICommandHandler<RecurringPaymentCommand, PaymentDto>
    {
        /// <param name="recurringPaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentDto> Handle(RecurringPaymentCommand recurringPaymentCommand,
            CancellationToken cancellationToken)
        {
            PaymentAggregate payment = await paymentService.ProcessRecurringPaymentAsync(
                recurringPaymentCommand.Amount,
                recurringPaymentCommand.DecimalPrecision,
                recurringPaymentCommand.CurrencyDesc,
                recurringPaymentCommand.PolicyId,
                recurringPaymentCommand.InvoiceNumber,
                recurringPaymentCommand.PayorId,
                recurringPaymentCommand.RenewedFromPolicyId,
                cancellationToken);
            return mapper.Map<PaymentDto>(payment);
        }
    }
}