using AutoMapper;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Queries.PaymentsQuery;
using CoverGo.Payments.Domain.Payment;
using FluentAssertions;
using MongoDB.Bson;
using MongoDB.Driver;
using Moq;

namespace CoverGo.Payments.UnitTests.Payments.Queries;

public class PaymentTokenFilterTests
{
    [Fact]
    public void GIVEN_tokenized_filter_WHEN_building_mongo_filter_THEN_should_filter_for_payments_with_token()
    {
        // Arrange
        var handler = CreateHandler();
        var filter = new PaymentsWhere
        {
            PaymentToken = new PaymentTokenWhere
            {
                In = new[] { PaymentTokenStatus.TOKENIZED }
            }
        };

        // Act
        var mongoFilter = handler.GetFilterPublic(filter);

        // Assert
        mongoFilter.Should().NotBeNull();
        // The filter should check for non-null InitialBearer and non-null/non-empty token
    }

    [Fact]
    public void GIVEN_not_tokenized_filter_WHEN_building_mongo_filter_THEN_should_filter_for_payments_without_token()
    {
        // Arrange
        var handler = CreateHandler();
        var filter = new PaymentsWhere
        {
            PaymentToken = new PaymentTokenWhere
            {
                In = new[] { PaymentTokenStatus.NO_TOKENIZED }
            }
        };

        // Act
        var mongoFilter = handler.GetFilterPublic(filter);

        // Assert
        mongoFilter.Should().NotBeNull();
        // The filter should check for null InitialBearer or null/empty token
    }

    [Fact]
    public void GIVEN_both_tokenized_and_not_tokenized_filter_WHEN_building_mongo_filter_THEN_should_use_or_condition()
    {
        // Arrange
        var handler = CreateHandler();
        var filter = new PaymentsWhere
        {
            PaymentToken = new PaymentTokenWhere
            {
                In = new[] { PaymentTokenStatus.TOKENIZED, PaymentTokenStatus.NO_TOKENIZED }
            }
        };

        // Act
        var mongoFilter = handler.GetFilterPublic(filter);

        // Assert
        mongoFilter.Should().NotBeNull();
        // The filter should use OR condition to include both tokenized and non-tokenized payments
    }

    private TestablePaymentsQueryHandler CreateHandler()
    {
        var mockMapper = new Mock<IMapper>();
        var mockCollection = new Mock<IMongoCollection<PaymentAggregate>>();
        return new TestablePaymentsQueryHandler(mockMapper.Object, mockCollection.Object);
    }

    // Test wrapper to expose protected method
    private class TestablePaymentsQueryHandler : PaymentsQueryHandler
    {
        public TestablePaymentsQueryHandler(IMapper mapper, IMongoCollection<PaymentAggregate> mongoCollection)
            : base(mapper, mongoCollection)
        {
        }

        public FilterDefinition<PaymentAggregate> GetFilterPublic(PaymentsWhere? filter)
        {
            return GetFilter(filter);
        }
    }
}
