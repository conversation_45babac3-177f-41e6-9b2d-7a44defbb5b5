﻿using System.Text.Json;
using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.InitializePayment
{
    public class InitializePaymentCommandHandler(IMapper mapper, IPaymentService paymentService)
        : ICommandHandler<InitializePaymentCommand, ProcessInitialPaymentResultDto>
    {
        /// <param name="initializePaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<ProcessInitialPaymentResultDto> Handle(InitializePaymentCommand initializePaymentCommand,
            CancellationToken cancellationToken)
        {
            JsonDocument? dynamicFieldsDocument = null;
            try
            {
                PreauthPaymentAggregate payment;
                JsonElement? dynamicFields;
                if (initializePaymentCommand.InitializationToken != null)
                {
                    TokenizedPaymentInitializationAggregate? tokenizedPaymentInitializationAggregate =
                        await paymentService.GetTokenizedPaymentInitializationAsync(
                            initializePaymentCommand.InitializationToken,
                            throwIfNotFound: true,
                            cancellationToken);

                    tokenizedPaymentInitializationAggregate!.ThrowIfCanceled();

                    payment = new PreauthPaymentAggregate(
                        tokenizedPaymentInitializationAggregate.PaymentProvider,
                        new PaymentMoney(
                            tokenizedPaymentInitializationAggregate.PaymentCurrencyCode,
                            tokenizedPaymentInitializationAggregate.PaymentCurrencyDesc,
                            tokenizedPaymentInitializationAggregate.PaymentAmount,
                            tokenizedPaymentInitializationAggregate.PaymentDecimalPrecision
                        ),
                        tokenizedPaymentInitializationAggregate.PolicyId,
                        tokenizedPaymentInitializationAggregate.InvoiceNumber,
                        tokenizedPaymentInitializationAggregate.PayorId,
                        null,
                        initializePaymentCommand.InitializationToken,
                        false,
                        tokenizedPaymentInitializationAggregate.IsCardUpdate ?? false
                    );

                    dynamicFieldsDocument = string.IsNullOrEmpty(tokenizedPaymentInitializationAggregate.DynamicFields)
                        ? null
                        : JsonDocument.Parse(tokenizedPaymentInitializationAggregate.DynamicFields);
                    dynamicFields = dynamicFieldsDocument?.RootElement;
                }
                else
                {
                    payment = new PreauthPaymentAggregate(
                        initializePaymentCommand.PaymentProvider!.Value,
                        new PaymentMoney(
                            initializePaymentCommand.CurrencyCode!,
                            initializePaymentCommand.CurrencyDesc!,
                            initializePaymentCommand.Amount!.Value,
                            initializePaymentCommand.DecimalPrecision!.Value
                        ),
                        initializePaymentCommand.PolicyId!,
                        initializePaymentCommand.InvoiceNumber!,
                        initializePaymentCommand.PayorId!,
                        null
                    );

                    dynamicFields = initializePaymentCommand.DynamicFields;
                }

                string? dynamicFieldsStr = dynamicFields?.GetRawText();
                if (!string.IsNullOrWhiteSpace(dynamicFieldsStr))
                {
                    payment.SetDynamicFields(dynamicFieldsStr);
                }
                
                ProcessInitialPaymentResult processInitialPaymentResult =
                    await paymentService.ProcessInitialPaymentAsync(
                        payment,
                        dynamicFields,
                        cancellationToken
                    );

                ProcessInitialPaymentResultDto? result =
                    mapper.Map<ProcessInitialPaymentResultDto>(processInitialPaymentResult);

                return result;
            }
            finally
            {
                dynamicFieldsDocument?.Dispose();
            }
        }
    }
}