using CoverGo.Payments.Application.HttpContextUtils;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Payments.Application;

public static class ApplicationServiceExtensions
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        services.AddAutoMapper(typeof(ApplicationAssemblyMarker).Assembly);
        services.AddScoped<IUserClaimExtractor, UserClaimExtractor>();

        return services;
    }
}