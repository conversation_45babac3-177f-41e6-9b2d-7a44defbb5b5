﻿using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;

namespace CoverGo.Payments.Application.Payments.Commands.CancelPaymentInitializations;

public class CancelPaymentInitializationsCommandHandler(IPaymentService paymentService)
    : ICommandHandler<CancelPaymentInitializationsCommand, PaymentInitializationsCancellationResultDto>
{
    public async Task<PaymentInitializationsCancellationResultDto> Handle(CancelPaymentInitializationsCommand command, CancellationToken cancellationToken)
    {
        await paymentService.CancelPaymentInitializationsAsync(
            command.InitializationTokens, 
            command.CancellationStatus, 
            command.CancellationReason, 
            cancellationToken);
        return new PaymentInitializationsCancellationResultDto { IsSuccessful = true };
    }
}
