﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.FinalizePayment
{
    public class FinalizePaymentCommandHandler(IMapper mapper, IPaymentService paymentService)
        : ICommandHandler<FinalizePaymentCommand, PaymentDto>
    {
        /// <param name="finalizePaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentDto> Handle(FinalizePaymentCommand finalizePaymentCommand,
            CancellationToken cancellationToken)
        {
            PaymentAggregate payment = await paymentService.FinalizePreauthPaymentAsync(
                finalizePaymentCommand.PaymentId, finalizePaymentCommand.DynamicFields, false, cancellationToken);
            return mapper.Map<PaymentDto>(payment);
        }
    }
}