﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.CapturePayment
{
    public class CapturePaymentCommandHandler(IMapper mapper, IPaymentService paymentService)
        : ICommandHandler<CapturePaymentCommand, PaymentDto>
    {
        /// <param name="capturePaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentDto> Handle(CapturePaymentCommand capturePaymentCommand,
            CancellationToken cancellationToken)
        {
            PaymentAggregate payment = await paymentService.CapturePreauthPaymentAsync(capturePaymentCommand.PaymentId, cancellationToken);
            return mapper.Map<PaymentDto>(payment);
        }
    }
}
