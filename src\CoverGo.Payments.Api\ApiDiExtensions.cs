using System.Diagnostics;
using CoverGo.BuildingBlocks.Api.GraphQl.Helpers;
using CoverGo.Extensions.DependencyInjection;
using CoverGo.Payments.Api.GraphQlConfiguration;
using CoverGo.Payments.Application.Payments.Commands.CancelPayment;
using CoverGo.Payments.Application.Payments.Commands.CancelPaymentInitializations;
using CoverGo.Payments.Application.Payments.Commands.CapturePayment;
using CoverGo.Payments.Application.Payments.Commands.FailPayment;
using CoverGo.Payments.Application.Payments.Commands.FinalizePayment;
using CoverGo.Payments.Application.Payments.Commands.InitializePayment;
using CoverGo.Payments.Application.Payments.Commands.InitializeUpdatePayment;
using CoverGo.Payments.Application.Payments.Commands.RecurringPayment;
using CoverGo.Payments.Application.Payments.Commands.RegisterPayment;
using CoverGo.Payments.Application.Payments.Commands.TokenizePaymentInitialization;
using CoverGo.Payments.Application.PspSettings.Commands.CreatePspSettings;
using CoverGo.Payments.Application.PspSettings.Commands.EncryptPspSettings;
using CoverGo.Payments.Application.Refunds.Commands.RefundPayment;
using HotChocolate.Execution.Configuration;
using HotChocolate.Stitching.SchemaDefinitions;
using Microsoft.AspNetCore.WebSockets;
using StackExchange.Redis;

namespace CoverGo.Payments.Api;

public static class ApiDiExtensions
{
    public static IServiceCollection AddApi(
        this IServiceCollection services,
        GraphQLStitchingOptions graphQlStitchingOptions,
        GraphQLOptions graphQlOptions)
    {
        services.AddMemoryCache();
        services.AddRouting();
        services.AddWebSockets(op => op.KeepAliveInterval = TimeSpan.FromSeconds(30));
        services.AddControllers();
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen();

        IRequestExecutorBuilder hcBuilder = services.AddPaymentsGraphQlSchema()
            .ModifyRequestOptions(options =>
            {
                if (!Debugger.IsAttached) options.ExecutionTimeout = TimeSpan.FromMinutes(2);
                options.IncludeExceptionDetails = graphQlOptions.IncludeExceptionDetails;
            });

        if (graphQlStitchingOptions.Enabled)
            hcBuilder
                .PublishSchemaDefinition(delegate (IPublishSchemaDefinitionDescriptor schema)
                {
                    schema.SetName(graphQlStitchingOptions.SchemaName!);

                    if (graphQlStitchingOptions.Redis?.Publish == true) schema.PublishToRedis(
                        // The configuration name under which the schema should be published
                        graphQlStitchingOptions.Redis!.ConfigurationName!,
                        // The connection multiplexer that should be used for publishing
                        sp => sp.GetRequiredService<IConnectionMultiplexer>());
                });

        return services;
    }

    public static IRequestExecutorBuilder AddPaymentsGraphQlSchema(
        this IServiceCollection services)
    {
        IRequestExecutorBuilder hcBuilder = services.AddCoverGoGraphQLServer()
            .BindRuntimeType<IDictionary<string, object?>, JsonType>()
            .InitializeOnStartup()
            .AddTypes();
        
        hcBuilder.ModifyOptions(o =>
        {
            o.EnableOneOf = true;
            o.RemoveUnreachableTypes = true;
        });
        hcBuilder.AddCommandType<TokenizePaymentInitializationCommand>();
        hcBuilder.AddCommandType<CancelPaymentInitializationsCommand>();
        hcBuilder.AddCommandType<InitializePaymentCommand>();
        hcBuilder.AddCommandType<InitializeUpdatePaymentCommand>();
        hcBuilder.AddCommandType<FailPaymentCommand>();
        hcBuilder.AddCommandType<CancelPaymentCommand>();
        hcBuilder.AddCommandType<CapturePaymentCommand>();
        hcBuilder.AddCommandType<FinalizePaymentCommand>();
        hcBuilder.AddCommandType<CreatePspSettingsCommand>();
        hcBuilder.AddCommandType<RefundPaymentCommand>();
        hcBuilder.AddCommandType<RecurringPaymentCommand>();
        hcBuilder.AddCommandType<RegisterPaymentCommand>();
        hcBuilder.AddCommandType<EncryptPspSettingsCommand>();
        
        hcBuilder.AddType<PaymentAggregateType>();
        hcBuilder.AddType<PspSettingsAggregateType>();
        hcBuilder.AddType<RefundAggregateType>();
        hcBuilder.AddType<PspBearerUnionType>();
        hcBuilder.AddType<PspBearerType>();
        hcBuilder.AddType<PspBearerPseudoCcType>();
        
        return hcBuilder;
    }
}