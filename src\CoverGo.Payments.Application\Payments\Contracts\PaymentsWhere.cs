﻿using CoverGo.Payments.Application.Common;

namespace CoverGo.Payments.Application.Payments.Contracts;

public class PaymentsWhere
{
    public IReadOnlyCollection<PaymentsWhere>? And { get; set; }
    public IReadOnlyCollection<PaymentsWhere>? Or { get; set; }
    public TransactionTypeWhere? TransactionType { get; set; }
    public PaymentSourceWhere? PaymentSource { get; set; }
    public PaymentStatusWhere? Status { get; set; }
    public DateTimeWhere? EffectiveDate { get; set; }
    public StringWhere? InvoiceNumber { get; set; }
}
