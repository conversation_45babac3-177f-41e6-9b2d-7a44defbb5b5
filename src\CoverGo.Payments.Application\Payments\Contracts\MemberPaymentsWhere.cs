using CoverGo.Payments.Application.Common;

namespace CoverGo.Payments.Application.Payments.Contracts;

public class MemberPaymentsWhere
{
    public IReadOnlyCollection<MemberPaymentsWhere>? And { get; set; }
    public IReadOnlyCollection<MemberPaymentsWhere>? Or { get; set; }
    public TransactionTypeWhere? TransactionType { get; set; }
    public PaymentSourceWhere? PaymentSource { get; set; }
    public PaymentStatusWhere? Status { get; set; }
    public DateTimeWhere? EffectiveDate { get; set; }
    public StringWhere? InvoiceNumber { get; set; }
    public StringWhere? PolicyId { get; set; }
    public StringWhere? PaymentId { get; set; }
    public StringWhere? PayorId { get; set; }
    public PaymentTokenWhere? PaymentToken { get; set; }
}