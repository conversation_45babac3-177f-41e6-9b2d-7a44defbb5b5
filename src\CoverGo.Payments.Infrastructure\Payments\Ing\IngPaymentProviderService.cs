using System.Collections.Specialized;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Exceptions;
using CoverGo.Payments.Domain.Helpers;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Infrastructure.Payments.Ing;
using CoverGo.Payments.Infrastructure.Payments.Ing.Configurations;
using CoverGo.Payments.Infrastructure.Payments.Ing.Models;
using CoverGo.Payments.Infrastructure.Payments.ING.Models;
using GuardClauses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.ING;

public class IngPaymentProviderService(
    ILogger<IngPaymentProviderService> logger,
    IIngClientFactory ingClientFactory,
    IHttpContextAccessor httpContextAccessor) : BasePaymentProviderService(logger)
{
    private const string HashMethod = "sha256";

    public override PaymentProvider Type => PaymentProvider.Ing;

    public override Task<RedirectUrlOutput> GetPreProcessRedirectUrlAsync(PreauthPaymentAggregate payment,
        JsonElement? dynamicFields,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        logger.LogInformation("Starting pre-process for payment ID: {PaymentId}", payment.Id);

        try
        {
            IngPspSettingsAggregate? pspSettings = GetPspSettings<IngPspSettingsAggregate>(payment.PspSettings);

            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));
            GuardClause.ArgumentIsNotNull(pspSettings!.MerchantId, nameof(pspSettings.MerchantId));
            GuardClause.ArgumentIsNotNull(pspSettings.ServiceId, nameof(pspSettings.ServiceId));
            GuardClause.ArgumentIsNotNull(pspSettings.ServiceKey, nameof(pspSettings.ServiceKey));

            payment.SetDynamicFields(dynamicFields);

            Customer customerData = ExtractCustomerData(dynamicFields);

            MapCustomerToPayerData(payment, customerData);

            Dictionary<string, object> data = PrepareFields(pspSettings, payment, customerData);
            string signature = CreateSignature(data, pspSettings.ServiceKey, HashMethod);
            data.Add("signature", signature);
            data.Add("environment", pspSettings.Environment ?? "qa");
            return Task.FromResult(new RedirectUrlOutput(null, ConvertToDictionary(data)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in pre-process for payment ID: {PaymentId}", payment.Id);
            throw;
        }
    }

    static Dictionary<string, string> ConvertToDictionary(Dictionary<string, object> fields)
    {
        var result = new Dictionary<string, string>();

        foreach ((string key, object? value) in fields)
            result[key] = value?.ToString() ?? string.Empty;

        return result;
    }


    private static Dictionary<string, object> PrepareFields(IngPspSettingsAggregate pspSettings,
        PaymentAggregate payment, Customer customerData)
    {
        var fields = new Dictionary<string, object>
        {
            { "merchantId", pspSettings.MerchantId },
            { "serviceId", pspSettings.ServiceId },
            { "amount", CurrencyHelper.ConvertUnitsToSubunits(payment.Money.PaymentAmount) },
            { "currency", CurrencyHelper.GetCurrency(payment.Money.PaymentCurrencyCode) },
            { "orderId", payment.InternalReference },
            { "orderDescription", payment.GetDescription() },
            { "customerFirstName", customerData.FirstName },
            { "customerLastName", customerData.LastName },
            { "customerEmail", customerData.Email },
            { "customerPhone", customerData.Phone },
            { "customerId", customerData.Id },
            { "urlSuccess", AppendPaymentIdToUrl(pspSettings.SuccessUrl, payment.Id) },
            { "urlFailure", pspSettings.FailUrl },
            { "urlReturn", pspSettings.RedirectUrl }
        };

        return fields.Where(entry => entry.Value != null)
            .ToDictionary(entry => entry.Key, entry => entry.Value);
    }
    
    private static string AppendPaymentIdToUrl(string? url, string paymentId)
    {
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(url, nameof(url));

        var uriBuilder = new UriBuilder(url!);
        NameValueCollection query = System.Web.HttpUtility.ParseQueryString(uriBuilder.Query);
        query["paymentId"] = paymentId;
        uriBuilder.Query = query.ToString();

        return uriBuilder.ToString();
    }

    private Customer ExtractCustomerData(JsonElement? dynamicFields)
    {
        GuardClause.ArgumentIsNotNull(dynamicFields, nameof(dynamicFields));

        try
        {
            Customer? customer = ParseDynamicFields<Customer>(dynamicFields!.Value.GetRawText());

            GuardClause.ArgumentIsNotNull(customer, nameof(customer));
            GuardClause.ArgumentIsNotNull(customer!.FirstName, nameof(customer.FirstName));
            GuardClause.ArgumentIsNotNull(customer.LastName, nameof(customer.LastName));
            GuardClause.ArgumentIsNotNull(customer.Id, nameof(customer.Id));
            GuardClause.ArgumentIsNotNull(customer.Email, nameof(customer.Email));

            return customer;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error when extracting the customer data from dynamic fields");
            throw;
        }
    }

    private static string CreateSignature(Dictionary<string, object> fields, string serviceKey, string hashMethod)
    {
        string data = PrepareData(fields);
        byte[] hashBytes = SHA256.HashData(Encoding.UTF8.GetBytes(data + serviceKey));
        return $"{BitConverter.ToString(hashBytes).Replace("-", "").ToLower()};{hashMethod}";
    }

    private static string PrepareData(Dictionary<string, object> data, string prefix = "") =>
        string.Join("&", data.OrderBy(kv => kv.Key)
            .Select(kv => kv.Value is Dictionary<string, object> nestedData
                ? PrepareData(nestedData, prefix + kv.Key + "[")
                : $"{prefix}{kv.Key}={kv.Value}"));

    public override Task CancelPreauthPaymentAsync(PreauthPaymentAggregate payment,
        CancellationToken cancellationToken = default) => throw new NotImplementedException();

    public override Task<CapturePaymentAggregate> CapturePaymentAsync(CapturePaymentAggregate capturePayment,
        string providerPaymentId,
        CancellationToken cancellationToken = default)
    {
        capturePayment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, capturePayment.Money);
        return Task.FromResult(capturePayment);
    }

    public override async Task<RecurringPaymentAggregate> RecurringPaymentAsync(
        RecurringPaymentAggregate recurringPayment,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(recurringPayment, nameof(recurringPayment));

        logger.LogInformation("Processing recurring payment for payment ID: {PaymentId}", recurringPayment.Id);

        try
        {
            IngPspSettingsAggregate? pspSettings =
                GetPspSettings<IngPspSettingsAggregate>(recurringPayment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            var pspBearerPseudoCc = recurringPayment.InitialBearer as PSPBearerPseudoCC;
            GuardClause.ArgumentIsNotNull(pspBearerPseudoCc, nameof(pspBearerPseudoCc));

            string paymentProfileId = pspBearerPseudoCc!.Token!;
            string serviceId = pspSettings!.ServiceId;
            decimal amount = CurrencyHelper.ConvertUnitsToSubunits(recurringPayment.Money.PaymentAmount);
            string currency = CurrencyHelper.GetCurrency(recurringPayment.Money.PaymentCurrencyCode);
            string orderId = recurringPayment.InternalReference;
            string title = recurringPayment.GetDescription();

            if (string.IsNullOrEmpty(paymentProfileId))
            {
                logger.LogError("No PaymentProfileID found for recurring payment ID: {PaymentId}", recurringPayment.Id);
                throw new Exception("No PaymentProfileID found for recurring payment.");
            }

            var debitRequest = new DebitPaymentProfileRequest
            {
                ServiceId = serviceId,
                PaymentProfileId = paymentProfileId,
                Amount = amount,
                Currency = currency,
                OrderId = orderId,
                Title = title
            };

            IIngClient ingClient = CreateIngClient(pspSettings);

            DebitPaymentProfileResponse? response =
                await ingClient.DebitPaymentProfileAsync(debitRequest, cancellationToken);

            ProcessRecurringResponse(response?.Transaction, recurringPayment);

            logger.LogInformation(
                "Recurring payment processed successfully for payment ID: {PaymentId}. Transaction ID: {TransactionId}",
                recurringPayment.Id, response!.Transaction.Id);

            return recurringPayment;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing recurring payment for payment ID: {PaymentId}", recurringPayment.Id);
            throw;
        }
    }

    private void ProcessRecurringResponse(Transaction? transaction, PaymentAggregate recurringPayment)
    {
        if (transaction == null)
        {
            string error = $"Failed to process recurring payment for payment ID: {recurringPayment.Id}";
            logger.LogError(error);
            recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, recurringPayment.Money, error);
            throw new DomainException($"Recurring payment request failed: {error}");
        }

        recurringPayment.AssignProviderTransaction(transaction.Id);

        if (recurringPayment.InitialBearer is PSPBearerPseudoCC initialBearer)
        {
            initialBearer.OrderId = transaction.Id;
            recurringPayment.UpdateInitialBearer(initialBearer);
        }

        recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, recurringPayment.Money);
    }

    public override async Task FailPaymentAsync(PaymentAggregate payment, CancellationToken cancellationToken = default)
    {
        payment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, payment.Money, "Failed by PSP.");
        await Task.CompletedTask;
    }

    public override async Task FinalizePaymentAsync(PreauthPaymentAggregate payment,
        PreauthPaymentAggregate? prevPayment, JsonElement? dynamicFields,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(payment, nameof(payment.DynamicFields));

        IngPspSettingsAggregate? pspSettings = GetPspSettings<IngPspSettingsAggregate>(payment.PspSettings);

        GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));
        GuardClause.ArgumentIsNotNull(pspSettings!.MerchantId, nameof(pspSettings.MerchantId));
        GuardClause.ArgumentIsNotNull(pspSettings.ApiKey, nameof(pspSettings.ApiKey));
        GuardClause.ArgumentIsNotNull(pspSettings.ApiUrl, nameof(pspSettings.ApiUrl));
        
        logger.LogInformation("Finalizing payment for payment ID: {PaymentId}", payment.Id);

        string customerId = ExtractCustomerId(payment.DynamicFields!);

        try
        {
            IIngClient ingClient = CreateIngClient(pspSettings);

            PaymentProfileResponse? paymentProfileResponse =
                await ingClient.GetPaymentProfileAsync(customerId, cancellationToken);
            if (paymentProfileResponse?.PaymentProfiles == null)
            {
                logger.LogError("Failed to retrieve payment profile for customer ID: {CustomerId}", customerId);
                throw new Exception("Failed to retrieve payment profile.");
            }

            PaymentProfile? paymentProfile = paymentProfileResponse.PaymentProfiles.FirstOrDefault();

            if (paymentProfile != null)
                MapPaymentInfo(payment, paymentProfile);

            payment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, payment.Money);

            logger.LogInformation("Payment profile successfully stored for payment ID: {PaymentId}", payment.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error finalizing payment for payment ID: {PaymentId}", payment.Id);
            throw;
        }
    }

    private string ExtractCustomerId(string paymentDynamicFieldsJsonString)
    {
        using var document = JsonDocument.Parse(paymentDynamicFieldsJsonString);
        JsonElement rootElement = document.RootElement;
        Customer customerData = ExtractCustomerData(rootElement);
        return customerData.Id;
    }

    private static void MapCustomerToPayerData(PaymentAggregate payment, Customer customer) =>
        payment.SetPayerData(new PayerData(
            language: null,
            emailAddress: customer.Email,
            address: null,
            lastName: customer.LastName,
            firstName: customer.FirstName,
            externalCustomerId: customer.Id,
            companyName: string.Empty,
            phoneNumber: customer.Phone));

    private void MapPaymentInfo(PaymentAggregate payment, PaymentProfile paymentProfile)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(paymentProfile, nameof(paymentProfile));

        if (string.IsNullOrWhiteSpace(payment.ExternalReference))
        {
            payment.SetExternalReference(paymentProfile.Id);
        }

        PSPBearerPseudoCC initialBearer = CreateInitialBearer(paymentProfile!);
        payment.SetInitialBearer(initialBearer);
    }

    private PSPBearerPseudoCC CreateInitialBearer(PaymentProfile paymentProfile)
    {
        GuardClause.ArgumentIsNotNull(paymentProfile, nameof(paymentProfile));

        return new PSPBearerPseudoCC
        {
            Token = paymentProfile.Id,
            PseudoCardPan = paymentProfile.MaskedNumber,
            CardType = paymentProfile.Organization,
            ExpiryMonth = int.TryParse(paymentProfile.Month, out int month) ? month : null,
            ExpiryYear = int.TryParse(paymentProfile.Year, out int year) ? year : null,
            Holder = $"{paymentProfile.FirstName} {paymentProfile.LastName}",
            TruncatedCardPan = paymentProfile.MaskedNumber,
        };
    }

    private IIngClient CreateIngClient(IngPspSettingsAggregate pspSettings) => ingClientFactory.Create(
        new IngClientSettings(pspSettings.ApiUrl, pspSettings.ApiVersion, pspSettings.MerchantId, pspSettings.ApiKey,
            "IngHttpClient", pspSettings.RetryCount));

    public override async Task<RefundAggregate> RefundAsync(PaymentAggregate payment, RefundAggregate paymentRefund,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(paymentRefund, nameof(paymentRefund));
        logger.LogInformation("Starting refund for payment ID: {PaymentId}", payment.Id);

        try
        {
            IngPspSettingsAggregate? pspSettings = GetPspSettings<IngPspSettingsAggregate>(payment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            IIngClient ingClient = CreateIngClient(pspSettings);

            RefundRequest refundRequest = BuildRefundRequest(payment, paymentRefund, pspSettings);

            GuardClause.IsNullOrEmptyStringOrWhiteSpace(payment.ProviderPaymentId, nameof(payment.ProviderPaymentId));

            RefundResponse? response =
                await ingClient.PostRefundAsync(refundRequest, payment.ProviderPaymentId!, cancellationToken);

            ProcessRefundResponse(payment, paymentRefund, response);

            return paymentRefund;
        }
        catch (Exception ex)
        {
            HandleRefundException(paymentRefund, ex, payment.Id);
            throw;
        }
    }

    private static RefundRequest BuildRefundRequest(PaymentAggregate payment, RefundAggregate paymentRefund,
        IngPspSettingsAggregate pspSettings)
    {
        var refundRequest = new RefundRequest
        {
            Type = "refund",
            ServiceId = pspSettings.ServiceId,
            Amount = CurrencyHelper.ConvertUnitsToSubunits(paymentRefund.Money.PaymentAmount),
        };

        return refundRequest;
    }

    private void ProcessRefundResponse(PaymentAggregate payment, RefundAggregate paymentRefund, RefundResponse response)
    {
        if (response.Transaction.Status == "settled")
        {
            paymentRefund.AssignProviderTransaction(response.Transaction.Id);
            paymentRefund.SetStatus(RefundStatus.Succeeded);
            logger.LogInformation("Refund completed successfully");
        }
        else
        {
            paymentRefund.SetStatus(RefundStatus.Failed, response.Transaction.Status);
            logger.LogWarning("Refund failed with status: {StatusDesc}", response.Transaction.Status);
            throw new DomainException($"Refund request failed: {response.Transaction.Status}");
        }
    }

    private void HandleRefundException(RefundAggregate paymentRefund, Exception ex, string paymentId)
    {
        logger.LogError(ex, "Error processing refund for payment ID: {PaymentId}", paymentId);
        paymentRefund.SetStatus(RefundStatus.Failed, ex.Message);
    }

    public override async
        Task<(string paymentId, string providerPaymentId, string externalReference, string internalReference,
            PaymentStatus paymentStatus, decimal
            ? amount, string webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip)> HandleWebhookAsync(
            string webhookBody, PspSettingsAggregate pspSettings,
            CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Handling the webhook");

        var ingPspSettingsAggregate = pspSettings as IngPspSettingsAggregate;
        GuardClause.ArgumentIsNotNull(ingPspSettingsAggregate, nameof(ingPspSettingsAggregate));

        HttpContext httpContext = httpContextAccessor.HttpContext ??
                                  throw new InvalidOperationException("No active HTTP context.");

        try
        {
            if (!httpContext.Request.Headers.TryGetValue("X-Imoje-Signature", out StringValues ingSignature))
            {
                logger.LogWarning("Missing X-Imoje-Signature header");
                return (string.Empty, string.Empty, string.Empty, string.Empty, PaymentStatus.Failed, null,
                    "Missing X-Imoje-Signature header", false, false);
            }

            WebhookRequest? webhookRequest = JsonConvert.DeserializeObject<WebhookRequest>(webhookBody);
            if (webhookRequest == null || webhookRequest.Transaction == null)
            {
                logger.LogInformation("Failed to handle the webhook: Invalid webhook body {webhookBody}", webhookBody);
                return (string.Empty, string.Empty, string.Empty, string.Empty, PaymentStatus.Failed, null,
                    "Invalid webhook body",
                    false, false);
            }

            return await HandleWebhookRequestAsync(webhookRequest);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An unexpected error occurred while handling webhook event");
            return (string.Empty, string.Empty, string.Empty, string.Empty, PaymentStatus.Failed, null,
                "Unexpected error occurred",
                false, false);
        }
    }

    private Task<(string paymentId, string providerPaymentId, string externalReference, string internalReference,
            PaymentStatus paymentStatus,
            decimal? amount, string webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip)>
        HandleWebhookRequestAsync(
            WebhookRequest webhookRequest)
    {
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(webhookRequest.Transaction.OrderId,
            nameof(webhookRequest.Transaction.OrderId));
        string providerPaymentId = webhookRequest.Transaction.Id;
        PaymentStatus paymentStatus = MapTransactionStatus(webhookRequest.Transaction.Status);
        decimal? amount = CurrencyHelper.ConvertSubunitsToUnits(webhookRequest.Transaction.Amount);

        return Task.FromResult((string.Empty, providerPaymentId, string.Empty, webhookRequest.Transaction.OrderId,
            paymentStatus, amount, string.Empty, false,
            false));
    }

    private static PaymentStatus MapTransactionStatus(string transactionStatus) =>
        transactionStatus switch
        {
            "new" => PaymentStatus.Prepared,
            "pending" => PaymentStatus.Prepared,
            "settled" => PaymentStatus.Finalizing,
            "cancelled" => PaymentStatus.Canceled,
            "rejected" => PaymentStatus.Failed,
            _ => PaymentStatus.Unmapped
        };
}