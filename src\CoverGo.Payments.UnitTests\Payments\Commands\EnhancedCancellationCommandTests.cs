using AutoMapper;
using CoverGo.Payments.Application.Payments.Commands.CancelPayment;
using CoverGo.Payments.Application.Payments.Commands.CancelPaymentInitializations;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace CoverGo.Payments.UnitTests.Payments.Commands
{
    public class EnhancedCancellationCommandTests
    {
        private readonly Mock<IPaymentService> _paymentServiceMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<ILogger<CancelPaymentCommandValidator>> _cancelPaymentLoggerMock;
        private readonly Mock<ILogger<CancelPaymentInitializationsCommandValidator>> _cancelInitLoggerMock;

        public EnhancedCancellationCommandTests()
        {
            _paymentServiceMock = new Mock<IPaymentService>();
            _mapperMock = new Mock<IMapper>();
            _cancelPaymentLoggerMock = new Mock<ILogger<CancelPaymentCommandValidator>>();
            _cancelInitLoggerMock = new Mock<ILogger<CancelPaymentInitializationsCommandValidator>>();
        }

        [Fact]
        public async Task GIVEN_CancelPaymentCommand_with_expired_status_WHEN_Handle_THEN_should_call_service_with_expired_status()
        {
            // Arrange
            var command = new CancelPaymentCommand("payment-id", PaymentStatus.Expired, "Payment expired due to timeout");
            var handler = new CancelPaymentCommandHandler(_mapperMock.Object, _paymentServiceMock.Object);
            
            var payment = new PreauthPaymentAggregate(
                PaymentProvider.Stripe,
                new PaymentMoney("978", "EUR", 1000, 2),
                "policy1",
                "invoice1",
                "payor1",
                null);

            _paymentServiceMock
                .Setup(s => s.CancelPreauthPaymentAsync("payment-id", PaymentStatus.Expired, "Payment expired due to timeout", It.IsAny<CancellationToken>()))
                .ReturnsAsync(payment);

            _mapperMock
                .Setup(m => m.Map<PaymentDto>(payment))
                .Returns(new PaymentDto());

            // Act
            await handler.Handle(command, CancellationToken.None);

            // Assert
            _paymentServiceMock.Verify(
                s => s.CancelPreauthPaymentAsync("payment-id", PaymentStatus.Expired, "Payment expired due to timeout", It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task GIVEN_CancelPaymentInitializationsCommand_with_expired_status_WHEN_Handle_THEN_should_call_service_with_expired_status()
        {
            // Arrange
            var tokens = new List<string> { "token1", "token2" };
            var command = new CancelPaymentInitializationsCommand(tokens, PaymentStatus.Expired, "Payments expired");
            var handler = new CancelPaymentInitializationsCommandHandler(_paymentServiceMock.Object);

            _paymentServiceMock
                .Setup(s => s.CancelPaymentInitializationsAsync(tokens, PaymentStatus.Expired, "Payments expired", It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await handler.Handle(command, CancellationToken.None);

            // Assert
            result.IsSuccessful.Should().BeTrue();
            _paymentServiceMock.Verify(
                s => s.CancelPaymentInitializationsAsync(tokens, PaymentStatus.Expired, "Payments expired", It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Theory]
        [InlineData(PaymentStatus.Canceled)]
        [InlineData(PaymentStatus.Expired)]
        [InlineData(PaymentStatus.Failed)]
        public void GIVEN_valid_cancellation_status_WHEN_validate_CancelPaymentCommand_THEN_should_pass_validation(PaymentStatus status)
        {
            // Arrange
            var validator = new CancelPaymentCommandValidator(_cancelPaymentLoggerMock.Object);
            var command = new CancelPaymentCommand("payment-id", status, "Test reason");

            // Act
            var result = validator.Validate(command);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Theory]
        [InlineData(PaymentStatus.Succeeded)]
        [InlineData(PaymentStatus.InProgress)]
        [InlineData(PaymentStatus.Pending)]
        public void GIVEN_invalid_cancellation_status_WHEN_validate_CancelPaymentCommand_THEN_should_fail_validation(PaymentStatus status)
        {
            // Arrange
            var validator = new CancelPaymentCommandValidator(_cancelPaymentLoggerMock.Object);
            var command = new CancelPaymentCommand("payment-id", status, "Test reason");

            // Act
            var result = validator.Validate(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(e => e.ErrorMessage.Contains("Invalid cancellation status"));
        }

        [Theory]
        [InlineData(PaymentStatus.Canceled)]
        [InlineData(PaymentStatus.Expired)]
        [InlineData(PaymentStatus.Failed)]
        public void GIVEN_valid_cancellation_status_WHEN_validate_CancelPaymentInitializationsCommand_THEN_should_pass_validation(PaymentStatus status)
        {
            // Arrange
            var validator = new CancelPaymentInitializationsCommandValidator(_cancelInitLoggerMock.Object);
            var command = new CancelPaymentInitializationsCommand(new List<string> { "token1" }, status, "Test reason");

            // Act
            var result = validator.Validate(command);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_null_cancellation_status_WHEN_validate_commands_THEN_should_pass_validation()
        {
            // Arrange
            var cancelPaymentValidator = new CancelPaymentCommandValidator(_cancelPaymentLoggerMock.Object);
            var cancelInitValidator = new CancelPaymentInitializationsCommandValidator(_cancelInitLoggerMock.Object);
            
            var cancelPaymentCommand = new CancelPaymentCommand("payment-id", null, "Test reason");
            var cancelInitCommand = new CancelPaymentInitializationsCommand(new List<string> { "token1" }, null, "Test reason");

            // Act
            var cancelPaymentResult = cancelPaymentValidator.Validate(cancelPaymentCommand);
            var cancelInitResult = cancelInitValidator.Validate(cancelInitCommand);

            // Assert
            cancelPaymentResult.IsValid.Should().BeTrue();
            cancelInitResult.IsValid.Should().BeTrue();
        }
    }
}
