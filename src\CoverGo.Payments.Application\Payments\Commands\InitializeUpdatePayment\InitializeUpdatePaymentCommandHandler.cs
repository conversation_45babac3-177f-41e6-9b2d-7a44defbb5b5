﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Helpers;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.InitializeUpdatePayment
{
    public class InitializeUpdatePaymentCommandHandler(IMapper mapper, IPaymentService paymentService)
        : ICommandHandler<InitializeUpdatePaymentCommand, ProcessInitialPaymentResultDto>
    {
        /// <param name="initializeUpdatePaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<ProcessInitialPaymentResultDto> Handle(
            InitializeUpdatePaymentCommand initializeUpdatePaymentCommand,
            CancellationToken cancellationToken)
        {
            var payment = new PreauthPaymentAggregate(
                initializeUpdatePaymentCommand.PaymentProvider,
                new PaymentMoney(
                    CurrencyHelper.GetIsoCode(initializeUpdatePaymentCommand.CurrencyDesc),
                    initializeUpdatePaymentCommand.CurrencyDesc,
                    GetInitialAmount(initializeUpdatePaymentCommand.PaymentProvider),
                    2
                ),
                initializeUpdatePaymentCommand.PolicyId,
                null,
                initializeUpdatePaymentCommand.PayorId,
                null,
                null,
                true
            );

            ProcessInitialPaymentResult processInitialPaymentResult =
                await paymentService.ProcessInitialPaymentAsync(
                    payment,
                    null,
                    cancellationToken
                );

            ProcessInitialPaymentResultDto? result =
                mapper.Map<ProcessInitialPaymentResultDto>(processInitialPaymentResult);

            return result;
        }

        private decimal GetInitialAmount(PaymentProvider paymentProvider)
        {
            return paymentProvider == PaymentProvider.Ing ? 0.05M : 0.00M;
        }
    }
}