﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Application.Refunds.Contracts;
using CoverGo.Payments.Domain.Refund;

namespace CoverGo.Payments.Application.Refunds.Commands.RefundPayment
{
    public class RefundPaymentCommandHandler(IMapper mapper, IPaymentService paymentService)
        : ICommandHandler<RefundPaymentCommand, PaymentRefundDto>
    {
        /// <param name="refundPaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentRefundDto> Handle(RefundPaymentCommand refundPaymentCommand,
            CancellationToken cancellationToken)
        {
            RefundAggregate refundPayment = await paymentService.RefundPaymentAsync(
                refundPaymentCommand.PaymentId, 
                refundPaymentCommand.Amount, 
                refundPaymentCommand.DecimalPrecision,
                cancellationToken);
            return mapper.Map<PaymentRefundDto>(refundPayment);
        }
    }
}
