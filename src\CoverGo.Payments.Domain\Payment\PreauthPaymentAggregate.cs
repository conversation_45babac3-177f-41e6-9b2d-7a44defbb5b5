﻿namespace CoverGo.Payments.Domain.Payment;

public class PreauthPaymentAggregate(
    PaymentProvider paymentProvider,
    PaymentMoney money,
    string policyId,
    string? invoiceNumber,
    string payorId,
    DateTime? effectiveDate,
    string? initializationToken = null,
    bool isUpdate = false,
    bool isCardUpdate = false)
    : PaymentAggregate(paymentProvider, money, policyId, invoiceNumber, payorId, effectiveDate)
{
    public PreauthPaymentStatus PreauthStatus { get; private set; }
    
    public bool IsUpdate { get; init; } = isUpdate;

    public bool IsCardUpdate { get; init; } = isCardUpdate;

    public string? InitializationToken { get; init; } = initializationToken;
    
    public string? PrevPaymentId { get; private set; }
    
    public void SetPrevPaymentId(string prevPaymentId) => PrevPaymentId = prevPaymentId;

    public void SetPreauthStatus(PreauthPaymentStatus preauthStatus) => PreauthStatus = preauthStatus;
}