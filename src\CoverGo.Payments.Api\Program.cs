using CoverGo.BuildingBlocks.Application.Core;
using CoverGo.BuildingBlocks.Auth.Extensions;
using CoverGo.BuildingBlocks.Bootstrapper.ApiBootstrapper;
using CoverGo.BuildingBlocks.MessageBus.Dapr;
using CoverGo.BuildingBlocks.MessageBus.Outbox;
using CoverGo.Payments.Api;
using CoverGo.Payments.Api.GraphQlConfiguration;
using CoverGo.Payments.Application;
using CoverGo.Payments.Infrastructure;
using StackExchange.Redis;
using CoverGo.BuildingBlocks.Scheduler.Hangfire;
using CoverGo.Applications.Monitoring;

WebApplicationBuilder webApplicationBuilder = WebApplication.CreateBuilder(args);
bool useInMemoryBus = webApplicationBuilder.Configuration.GetValue<bool>("UseInMemoryBus");
var checkDi = webApplicationBuilder.Configuration.GetValue<bool>("CheckDI");

webApplicationBuilder.Host.UseDefaultServiceProvider(options =>
{
    options.ValidateOnBuild = checkDi;
    options.ValidateScopes = checkDi;
});

ApiServiceBootstrapper webAppBuilder = ApiServiceBootstrapper
    .Initialize(webApplicationBuilder)
    .WithCoreSetup()
    .WithMultiTenantContext()
    .WithAuthentication()
    .WithLogging()
    .WithMetrics()
    .WithTracing()
    .WithCoreHealthCheck()
    .WithServiceConfiguration(services =>
    {
        services.AddMediatR(delegate(MediatRServiceConfiguration configuration)
        {
            configuration.RegisterServicesFromAssembly(typeof(InfrastructureServiceExtensions).Assembly);
        });

        services.AddHttpClient();

        services.AddCors(options =>
        {
            options.AddDefaultPolicy(
                builder =>
                {
                    builder.AllowAnyOrigin()
                        .AllowAnyHeader()
                        .AllowAnyMethod();
                });
        });

        services.AddApplication();
        services.AddInfrastructure(webApplicationBuilder.Configuration, useInMemoryBus);
        if (!useInMemoryBus)
        {
            services.AddHangfireScheduler();
        }
        
        services.AddCQRS(
            [
                typeof(ApplicationServiceExtensions).Assembly,
                typeof(InfrastructureServiceExtensions).Assembly
            ]
        );
        
        services.AddUserContextProviders();

        string? redisConnectionString = webApplicationBuilder.Configuration.GetConnectionString("redis");
        if (!string.IsNullOrEmpty(redisConnectionString))
        {
            services.AddSingleton<IConnectionMultiplexer>(_ => ConnectionMultiplexer.Connect(redisConnectionString));
        }

        if (!string.IsNullOrWhiteSpace(redisConnectionString))
        {
            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = redisConnectionString;
                options.InstanceName = $"{AppName}-";
            });
        }

        GraphQLStitchingOptions stitchingConfiguration =
            webApplicationBuilder.Configuration.GetRequiredSection("GraphQLStitching").Get<GraphQLStitchingOptions>() ??
            new GraphQLStitchingOptions();
        GraphQLOptions graphqlConfiguration =
            webApplicationBuilder.Configuration.GetRequiredSection("GraphQL").Get<GraphQLOptions>() ??
            new GraphQLOptions();
        services.AddApi(stitchingConfiguration, graphqlConfiguration)
            .AddCoverGoAuthorization(webApplicationBuilder.Configuration);

        services.AddMultiTenantFeatureManagement(webApplicationBuilder.Configuration);
    });

WebApplication app = webAppBuilder.BuildWebApp(app =>
{
    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI();
    }
    
    app.MapGraphQL();
    app.UseWebSockets();

    app.UseCors(z => z.AllowAnyOrigin().AllowAnyHeader());
    app.MapControllers();
    app.MapVersionEndpoint();
    if (!useInMemoryBus)
    {
        app
            .MessageBus(app.Configuration)
            .SubscribeToIntegrationEvents();

        app.RunOutboxMessageProcessor("*/1 * * * * *");

        app.AddHangfireDashboards();
    }
});

try
{
    app.Logger.LogInformation("Starting web host...");
    await app.RunWithGraphQLCommandsAsync(args);
}
catch (Exception ex)
{
    app.Logger.LogCritical(ex, "Host terminated unexpectedly...");
}

// This is to make autogenerated class Program public. It is required to allow it to be used for WebApplicationFactory<Program>.
// https://learn.microsoft.com/en-us/aspnet/core/test/integration-tests?view=aspnetcore-8.0#basic-tests-with-the-default-webapplicationfactory

public partial class Program
{
    private const string AppName = "covergo-payments";
}