﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.FailPayment
{
    public class FailPaymentCommandHandler(IMapper mapper, IPaymentService paymentService)
        : ICommandHandler<FailPaymentCommand, PaymentDto>
    {
        /// <param name="failPaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentDto> Handle(FailPaymentCommand failPaymentCommand,
            CancellationToken cancellationToken)
        {
            PaymentAggregate payment = await paymentService.FailPaymentAsync(failPaymentCommand.PaymentId, cancellationToken);
            return mapper.Map<PaymentDto>(payment);
        }
    }
}
