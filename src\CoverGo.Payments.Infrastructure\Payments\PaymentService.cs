﻿using System.Net;
using System.Text.Json;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.Domain.Core.DomainEvents;
using CoverGo.Multitenancy;
using CoverGo.Payments.Application.Payments.Commands.CancelPayment;
using CoverGo.Payments.Application.Payments.Commands.RegisterPayment;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Application.Payments.Providers;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Application.PspSettings.Providers;
using CoverGo.Payments.Application.Refunds.Commands.RefundPayment;
using CoverGo.Payments.Domain.Exceptions;
using CoverGo.Payments.Domain.Helpers;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.Payment.DomainEvents;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Infrastructure.Common;
using CoverGo.Payments.Infrastructure.Exceptions;
using CoverGo.Payments.Integration.Events;
using CoverGo.Proxies.Auth;
using GuardClauses;
using MediatR;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using PaymentAggregate = CoverGo.Payments.Domain.Payment.PaymentAggregate;

namespace CoverGo.Payments.Infrastructure.Payments;

public class PaymentService : IPaymentService
{
    public const string TransactionTypePremiumCollectedInitial = "Premium Collected Initial";
    public const string TransactionTypePremiumCollectedRecurring = "Premium Collected Recurring";
    public const string TransactionTypePremiumCollectedEndorsement = "Premium Collected Endorsement";
    public const string TransactionTypeRefund = "Refund";
    private const string TransactionTypeRefundPaid = "Refund Paid";

    public const string TransactionSucceededStatus = "Succeeded";
    public const string TransactionFailedStatus = "Failed";

    private const string MemberPortalClientKey = "member";

    private readonly ILogger<PaymentService> _logger;

    private readonly IRepository<TokenizedPaymentInitializationAggregate, string>
        _tokenizedPaymentInitializationRepository;

    private readonly IRepository<PaymentAggregate, string> _paymentRepository;
    private readonly IRepository<RefundAggregate, string> _refundRepository;
    private readonly IEnumerable<IPaymentProviderService> _paymentProviderServices;
    private readonly IEnumerable<IPspSettingsProvider> _pspSettingsProviders;
    private readonly IMongoCollection<PaymentAggregate> _mongoCollection;
    private readonly IAuthService _authService;
    private readonly ITenantProvider _tenantProvider;
    private readonly IMediator _mediator;

    public PaymentService(
        ILogger<PaymentService> logger,
        IRepository<TokenizedPaymentInitializationAggregate, string> tokenizedPaymentInitializationRepository,
        IRepository<PaymentAggregate, string> paymentRepository,
        IRepository<RefundAggregate, string> refundRepository,
        IEnumerable<IPaymentProviderService> paymentProviderServices,
        IEnumerable<IPspSettingsProvider> pspSettingsProviders,
        IMongoCollection<PaymentAggregate> mongoCollection,
        IAuthService authService,
        ITenantProvider tenantProvider,
        IMediator mediator)
    {
        GuardClause.ArgumentIsNotNull(logger, nameof(logger));
        GuardClause.ArgumentIsNotNull(tokenizedPaymentInitializationRepository,
            nameof(tokenizedPaymentInitializationRepository));
        GuardClause.ArgumentIsNotNull(paymentRepository, nameof(paymentRepository));
        GuardClause.ArgumentIsNotNull(refundRepository, nameof(refundRepository));
        GuardClause.ArgumentIsNotNull(authService, nameof(authService));
        GuardClause.ArgumentIsNotNull(tenantProvider, nameof(tenantProvider));
        GuardClause.ArgumentIsNotNull(mediator, nameof(mediator));

        _logger = logger;
        _tokenizedPaymentInitializationRepository = tokenizedPaymentInitializationRepository;
        _paymentRepository = paymentRepository;
        _refundRepository = refundRepository;
        _paymentProviderServices = paymentProviderServices;
        _pspSettingsProviders = pspSettingsProviders;
        _mongoCollection = mongoCollection;
        _authService = authService;
        _tenantProvider = tenantProvider;
        _mediator = mediator;
    }

    public IMongoQueryable<PaymentAggregate> GetPayments()
        => _mongoCollection.AsQueryable();

    public async Task<TokenizedPaymentInitialization> TokenizePaymentInitializationAsync(
        TokenizedPaymentInitializationAggregate tokenizedPaymentInitialization, CancellationToken cancellationToken)
    {
        await _tokenizedPaymentInitializationRepository.InsertAsync(tokenizedPaymentInitialization, cancellationToken);
        return new TokenizedPaymentInitialization { InitializationToken = tokenizedPaymentInitialization.Id };
    }

    public async Task CancelPaymentInitializationsAsync(List<string> initializationTokens,
        PaymentStatus? cancellationStatus = null,
        string? cancellationReason = null,
        CancellationToken cancellationToken = default)
    {
        List<TokenizedPaymentInitializationAggregate> aggregates =
            await _tokenizedPaymentInitializationRepository.FindAllAsync(ids: initializationTokens, cancellationToken);
        if (aggregates.Count == 0) return;
        // If the cancellation status is not expired, cancel the initializations
        // The payment link will be expired as automatically by public fields to make flow consistent
        if (cancellationStatus == PaymentStatus.Expired)
        {
            aggregates.ForEach(a => a.Expired());
        }
        else
        {
            aggregates.ForEach(a => a.Cancel());
        }
        await _tokenizedPaymentInitializationRepository.UpdateBatchAsync(aggregates, cancellationToken);
        var invoiceNumbers = aggregates
        .Where(a => !string.IsNullOrWhiteSpace(a.InvoiceNumber))
        .Select(a => a.InvoiceNumber!)
        .Distinct()
        .ToList();
        var payments = await _paymentRepository.FindAllByAsync(
            p => invoiceNumbers.Contains(p.InvoiceNumber!), cancellationToken);
        if (!payments.Any()) return;
        var cancelTasks = new List<Task>();
        foreach (PaymentAggregate payment in payments)
        {
            if (payment is PreauthPaymentAggregate preauthPayment &&
            initializationTokens.Contains(preauthPayment.InitializationToken!))
            {
                cancelTasks.Add(_mediator.Send(new CancelPaymentCommand(
                    preauthPayment.Id,
                    cancellationStatus,
                    cancellationReason), cancellationToken));
            }
        }

        await Task.WhenAll(cancelTasks);
    }

    public async Task<TokenizedPaymentInitializationAggregate?> GetTokenizedPaymentInitializationAsync(
        string initializationToken, bool throwIfNotFound, CancellationToken cancellationToken)
    {
        if (throwIfNotFound)
            return await _tokenizedPaymentInitializationRepository.GetByIdAsync(initializationToken, cancellationToken);

        return await _tokenizedPaymentInitializationRepository.FindByIdAsync(initializationToken, cancellationToken);
    }

    public async Task<ProcessInitialPaymentResult> ProcessInitialPaymentAsync(PreauthPaymentAggregate preauthPayment,
        JsonElement? dynamicFields,
        CancellationToken cancellationToken = default)
    {
        using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = preauthPayment.Id }))
        {
            ValidatePayment(preauthPayment);

            await PopulatePspSettingsAsync(preauthPayment, cancellationToken);

            preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.InProgress, preauthPayment.Money);

            PaymentAggregate? prevPayment =
                await GetPreauthPaymentByPolicyIdAndPayorId(preauthPayment.PolicyId, preauthPayment.PayorId,
                    cancellationToken);
            IPaymentProviderService pspService = GetPaymentProviderService(preauthPayment.PaymentProvider);
            if (prevPayment != null)
            {
                preauthPayment.SetPrevPaymentId(prevPayment.Id);
            }
            if (prevPayment != null && preauthPayment.IsUpdate)
            {
                if (!string.IsNullOrWhiteSpace(prevPayment.DynamicFields))
                {
                    dynamicFields = System.Text.Json.JsonSerializer.Deserialize<JsonElement>(prevPayment.DynamicFields!);
                }
                dynamicFields = await SetPostCheckoutRedirectUrlForUpdateAsync(pspService, dynamicFields,
                    cancellationToken);
                preauthPayment.SetDynamicFields(dynamicFields);
            }
            else
            {
                dynamicFields =
                    await SetPostCheckoutRedirectUrlAsync(pspService, dynamicFields, cancellationToken);
                preauthPayment.SetDynamicFields(dynamicFields);
            }

            SetInternalReference(prevPayment, preauthPayment);

            await VerifyPreviousPaymentByInvoiceNumberAsync(preauthPayment, cancellationToken);

            await _paymentRepository.InsertAsync(preauthPayment, cancellationToken);

            Uri? redirectUrl = default;
            Dictionary<string, string>? data = null;
            try
            {
                preauthPayment = await PreparePaymentAsync(preauthPayment, pspService, cancellationToken);
                if (preauthPayment.Status == PaymentStatus.Failed)
                {
                    await _paymentRepository.UpdateAsync(preauthPayment, cancellationToken);
                    return new ProcessInitialPaymentResult(preauthPayment);
                }

                RedirectUrlOutput redirectInfo =
                    await pspService.GetPreProcessRedirectUrlAsync(preauthPayment, dynamicFields, cancellationToken);

                redirectUrl = redirectInfo.RedirectUrl;
                data = redirectInfo.Data.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
            }
            catch (Exception exception)
            {
                if (!preauthPayment.Status.Equals(PaymentStatus.Failed))
                    preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, preauthPayment.Money,
                        exception.Message);
            }
            finally
            {
                await _paymentRepository.UpdateAsync(preauthPayment, cancellationToken);
            }

            if (preauthPayment.PreauthStatus != PreauthPaymentStatus.Cancelled &&
                preauthPayment.PreauthStatus != PreauthPaymentStatus.CancelFailed &&
                preauthPayment.PreauthStatus != PreauthPaymentStatus.ToBePaid)
                throw new IllegalPaymentStatusException(preauthPayment.PreauthStatus);

            if (preauthPayment.Status == PaymentStatus.Chargeback ||
                preauthPayment.Status == PaymentStatus.Created ||
                preauthPayment.Status == PaymentStatus.InProgress ||
                preauthPayment.Status == PaymentStatus.Refunded ||
                preauthPayment.Status == PaymentStatus.Unmapped)
                throw new IllegalPaymentStatusException(preauthPayment.Status);

            if (redirectUrl == null && data == null) return new ProcessInitialPaymentResult(preauthPayment);

            if (redirectUrl != null && data == null)
                return new ProcessInitialPaymentResult(preauthPayment, redirectUrl);

            return new ProcessInitialPaymentResult(preauthPayment, redirectUrl, data);
        }
    }

    private async Task<JsonElement?> SetPostCheckoutRedirectUrlForUpdateAsync(
        IPaymentProviderService paymentProviderService,
        JsonElement? previousDynamicFields,
        CancellationToken cancellationToken)
    {

        if (!_tenantProvider.TryGetCurrent(out TenantId? tenantId))
            return previousDynamicFields;

        TenantSettings tenantSettings = await _authService.GetTenantSettingsAsync(tenantId.Value, cancellationToken);

        string? memberPortalOrigin = GetSuccessRedirectUrlFromSettings(tenantSettings, MemberPortalClientKey);
        if (string.IsNullOrWhiteSpace(memberPortalOrigin))
            return previousDynamicFields;

        memberPortalOrigin = EnsureUrlHasSchema(memberPortalOrigin);

        // Add clientKey to dynamic fields
        JsonElement? updatedDynamicFields = AddClientKeyToExistingJson(previousDynamicFields, MemberPortalClientKey);

        return updatedDynamicFields is null
            ? SerializePostCheckoutRedirectUrl(memberPortalOrigin)
            : AddPostCheckoutRedirectUrlToExistingJson(updatedDynamicFields, memberPortalOrigin);
    }
    private async Task<JsonElement?> SetPostCheckoutRedirectUrlAsync(
            IPaymentProviderService paymentProviderService,
            JsonElement? previousDynamicFields,
            CancellationToken cancellationToken)
    {
        // Extract clientKey from previousDynamicFields and use it to get specific host from tenantSettings
        string? clientKey = ExtractClientKeyFromDynamicFields(previousDynamicFields);

        if (!string.IsNullOrWhiteSpace(clientKey))
        {
            string? clientSpecificRedirectUrl = await GetTenantConfigRedirectUrlAsync(clientKey, cancellationToken);
            if (!string.IsNullOrWhiteSpace(clientSpecificRedirectUrl))
            {
                return previousDynamicFields is null
                    ? SerializePostCheckoutRedirectUrl(clientSpecificRedirectUrl)
                    : AddPostCheckoutRedirectUrlToExistingJson(previousDynamicFields, clientSpecificRedirectUrl);
            }
        }
        return previousDynamicFields;
    }

    private static string? GetSuccessRedirectUrlFromSettings(TenantSettings? tenantSettings, string clientKey) =>
        tenantSettings?.Hosts?.FirstOrDefault(h => h.Contains(clientKey));

    private static string EnsureUrlHasSchema(string url) =>
        url.StartsWith("http://") || url.StartsWith("https://") ? url : $"https://{url}";

    private static JsonElement SerializePostCheckoutRedirectUrl(string origin)
    {
        var dictionary = new Dictionary<string, string>
        {
            ["postCheckoutRedirectUrl"] = origin,
            ["clientKey"] = MemberPortalClientKey
        };

        return System.Text.Json.JsonSerializer.SerializeToElement(dictionary);
    }

    private static JsonElement AddPostCheckoutRedirectUrlToExistingJson(JsonElement? existingJson, string origin)
    {
        if (existingJson is null)
            return SerializePostCheckoutRedirectUrl(origin);

        var jsonDictionary = existingJson.Value
            .EnumerateObject()
            .ToDictionary(property => property.Name, property => property.Value.Clone());

        jsonDictionary["postCheckoutRedirectUrl"] = System.Text.Json.JsonSerializer.SerializeToElement(origin);

        return System.Text.Json.JsonSerializer.SerializeToElement(jsonDictionary);
    }

    private static JsonElement AddClientKeyToExistingJson(JsonElement? existingJson, string clientKey)
    {
        if (existingJson is null)
        {
            var dictionary = new Dictionary<string, string>
            {
                ["clientKey"] = clientKey
            };
            return System.Text.Json.JsonSerializer.SerializeToElement(dictionary);
        }

        var jsonDictionary = existingJson.Value
            .EnumerateObject()
            .ToDictionary(property => property.Name, property => property.Value.Clone());

        jsonDictionary["clientKey"] = System.Text.Json.JsonSerializer.SerializeToElement(clientKey);

        return System.Text.Json.JsonSerializer.SerializeToElement(jsonDictionary);
    }

    public async Task<PaymentAggregate> CapturePreauthPaymentAsync(string preauthPaymentId,
        CancellationToken cancellationToken = default)
    {
        using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = preauthPaymentId }))
        {
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(preauthPaymentId, nameof(preauthPaymentId));

            var preauthPayment =
                await _paymentRepository.GetByIdAsync(preauthPaymentId, cancellationToken) as PreauthPaymentAggregate;

            IPspSettingsProvider pspSettingsProvider = GetPspSettingsProvider(preauthPayment!.PaymentProvider);
            PspSettingsAggregate? pspSettings = await pspSettingsProvider.GetPspSettingsAsync(cancellationToken);
            if (pspSettings?.UseAutoCapture ?? false)
                throw new DomainException(
                    "Capturing failed: The auto-capture is disabled in the current PSP settings.");

            if (preauthPayment.IsFailure || preauthPayment.PreauthStatus != PreauthPaymentStatus.ToBePaid)
                return preauthPayment;

            var capturePayment = new CapturePaymentAggregate(preauthPayment);
            ValidatePayment(capturePayment);
            capturePayment.AddPaymentStatusHistoryItem(PaymentStatus.Prepared, preauthPayment.Money);
            await _paymentRepository.InsertAsync(capturePayment, cancellationToken);

            try
            {
                IPaymentProviderService pspService = GetPaymentProviderService(preauthPayment.PaymentProvider);
                capturePayment =
                    await pspService.CapturePaymentAsync(capturePayment, preauthPayment.ProviderPaymentId,
                        cancellationToken);
                if (capturePayment.IsSuccessful) preauthPayment.SetPreauthStatus(PreauthPaymentStatus.Paid);

                return capturePayment;
            }
            catch (Exception ex)
            {
                if (!capturePayment.Status.Equals(PaymentStatus.Failed))
                {
                    preauthPayment.SetPreauthStatus(PreauthPaymentStatus.PaymentFailed);
                    capturePayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, capturePayment.Money, ex.Message);
                }
            }
            finally
            {
                await _paymentRepository.UpdateAsync(preauthPayment, cancellationToken);
                await _paymentRepository.UpdateAsync(capturePayment, cancellationToken);
            }

            return capturePayment;
        }
    }

    public async Task<PreauthPaymentAggregate> CancelPreauthPaymentAsync(string preauthPaymentId,
        PaymentStatus cancellationStatus = PaymentStatus.Canceled,
        string? cancellationReason = null,
        CancellationToken cancellationToken = default)
    {
        using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = preauthPaymentId }))
        {
            PaymentAggregate payment = await _paymentRepository.GetByIdAsync(preauthPaymentId, cancellationToken);
            if (payment is not PreauthPaymentAggregate preauthPayment)
                throw new DomainException("Cancellation failed: Only preauthorized transactions can be canceled.");

            // Validate the cancellation status
            ValidateCancellationStatus(cancellationStatus);

            try
            {
                IPaymentProviderService pspService = GetPaymentProviderService(preauthPayment.PaymentProvider);
                if (preauthPayment.ProviderPaymentId is null && preauthPayment.Status == PaymentStatus.Prepared)
                {
                    preauthPayment.SetPreauthStatus(GetPreauthStatusForCancellation(cancellationStatus));
                    preauthPayment.AddPaymentStatusHistoryItem(cancellationStatus, preauthPayment.Money, cancellationReason);

                    _logger.LogInformation(
                        "Preauth payment is in prepared state and not linked to provider transaction: {PaymentId} completed successfully with status {Status}",
                        preauthPayment.Id, cancellationStatus);
                }
                else if (preauthPayment.Status != PaymentStatus.Canceled
                && preauthPayment.Status != PaymentStatus.Expired)
                {
                    // For PSP cancellations, we still need to support the new status
                    await pspService.CancelPreauthPaymentAsync(preauthPayment, cancellationToken);

                    // Override the PSP-set status with the desired cancellation status if different
                    if (preauthPayment.Status != cancellationStatus)
                    {
                        preauthPayment.AddPaymentStatusHistoryItem(cancellationStatus, preauthPayment.Money, cancellationReason);
                        preauthPayment.SetPreauthStatus(GetPreauthStatusForCancellation(cancellationStatus));
                    }
                }
            }
            catch (Exception ex)
            {
                if (!preauthPayment.Status.Equals(PaymentStatus.Failed))
                    preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, preauthPayment.Money, ex.Message);
            }
            finally
            {
                await _paymentRepository.UpdateAsync(preauthPayment, cancellationToken);
            }

            return preauthPayment;
        }
    }

    public async Task<PreauthPaymentAggregate> FinalizePreauthPaymentAsync(string preauthPaymentId,
        JsonElement? dynamicFields,
        bool isWebhookCall = false,
        CancellationToken cancellationToken = default)
    {
        using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = preauthPaymentId }))
        {
            PaymentAggregate payment = await _paymentRepository.GetByIdAsync(preauthPaymentId, cancellationToken);
            if (payment is not PreauthPaymentAggregate preauthPayment)
                throw new DomainException("Finalization failed: Only preauthorized transactions can be finalized.");

            if (!isWebhookCall)
            {
                if (preauthPayment.IsFailure || preauthPayment.IsSuccessful) return preauthPayment;
            }

            try
            {
                IPaymentProviderService pspService = GetPaymentProviderService(preauthPayment.PaymentProvider);
                PaymentAggregate? prevPayment = null;
                if (preauthPayment.IsUpdate)
                {
                    if (string.IsNullOrWhiteSpace(preauthPayment.PrevPaymentId))
                        throw new DomainException("Finalization failed: PrevPaymentId is missing.");
                    prevPayment =
                        await _paymentRepository.GetByIdAsync(preauthPayment.PrevPaymentId!, cancellationToken);
                    if (prevPayment == null)
                        throw new DomainException(
                            "Finalization failed: Previously preauthorized transaction is missing.");

                }

                await pspService.FinalizePaymentAsync(preauthPayment, prevPayment as PreauthPaymentAggregate,
                    dynamicFields,
                    cancellationToken);
            }
            catch (Exception exception)
            {
                if (!preauthPayment.Status.Equals(PaymentStatus.Failed))
                    preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, preauthPayment.Money,
                        exception.Message);
            }
            finally
            {
                if (preauthPayment.IsUpdate || preauthPayment.IsCardUpdate)
                {
                    if (preauthPayment.Status == PaymentStatus.Succeeded)
                    {
                        AddCreditCardUpdateDomainEvent(preauthPayment);
                        // when success payment push refund for update card flow
                        await _mediator.Send(new RefundPaymentCommand(
                                preauthPayment.Id,
                                preauthPayment.Money.PaymentAmount,
                                preauthPayment.Money.PaymentDecimalPrecision
                         ), cancellationToken);
                    }
                }
                else
                    AddPaymentDomainEvent(preauthPayment, PaymentType.Initial);

                await _paymentRepository.UpdateAsync(preauthPayment, cancellationToken);
            }

            if (preauthPayment is { IsSuccessful: false, IsFailure: false })
                throw new IllegalPaymentStatusException(preauthPayment.Status);

            return preauthPayment;
        }
    }

    public async Task<RecurringPaymentAggregate> ProcessRecurringPaymentAsync(
        decimal amount,
        int decimalPrecision,
        string currencyDesc,
        string policyId,
        string invoiceNumber,
        string payorId,
        string? renewedFromPolicyId,
        CancellationToken cancellationToken = default)
    {
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(policyId, nameof(policyId));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(invoiceNumber, nameof(invoiceNumber));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(payorId, nameof(payorId));
        GuardClause.IsZeroOrNegative(decimalPrecision, nameof(decimalPrecision));
        GuardClause.IsZeroOrNegative(amount, nameof(amount));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(currencyDesc, nameof(currencyDesc));

        PaymentAggregate? payment =
            await GetPreauthPaymentByPolicyIdAndPayorId(renewedFromPolicyId ?? policyId, payorId, cancellationToken);
        if (payment is null) throw new DomainException($"Unable to locate the initial payment for payorId: '{payorId}', policyId: '{policyId}', renewedFromPolicyId: '{renewedFromPolicyId}'.");

        var recurringPayment = new RecurringPaymentAggregate(payment.PaymentProvider,
            new PaymentMoney(CurrencyHelper.GetIsoCode(currencyDesc), currencyDesc, amount,
                decimalPrecision), policyId ?? renewedFromPolicyId, invoiceNumber, payorId, null);

        using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = recurringPayment.Id }))
        {
            recurringPayment.SetInitialBearer(payment.InitialBearer!);
            recurringPayment.SetPspSettings(payment.PspSettings);
            recurringPayment.SetPayerData(payment.PayerData!);
            recurringPayment.SetDynamicFields(payment.DynamicFields);

            PaymentAggregate? prevPayment =
                await GetPrevPaymentByPolicyIdAndPayorId(recurringPayment.PolicyId, recurringPayment.PayorId,
                    cancellationToken);
            SetInternalReference(prevPayment, recurringPayment);

            await VerifyPreviousPaymentByInvoiceNumberAsync(recurringPayment, cancellationToken);

            ValidatePayment(recurringPayment);

            recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Prepared, recurringPayment.Money);
            await _paymentRepository.InsertAsync(recurringPayment, cancellationToken);

            try
            {
                IPaymentProviderService pspService = GetPaymentProviderService(recurringPayment.PaymentProvider);
                recurringPayment =
                    await pspService.RecurringPaymentAsync(recurringPayment, cancellationToken);
            }
            catch (Exception ex)
            {
                if (!recurringPayment.Status.Equals(PaymentStatus.Failed))
                    recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, recurringPayment.Money,
                        ex.Message);
            }
            finally
            {
                AddPaymentDomainEvent(recurringPayment, PaymentType.Recurring);
                await _paymentRepository.UpdateAsync(recurringPayment, cancellationToken);
            }

            return recurringPayment;
        }
    }

    private async Task VerifyPreviousPaymentByInvoiceNumberAsync(PaymentAggregate payment,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(payment.InvoiceNumber))
            return;
        IEnumerable<PaymentAggregate> paymentsByInvoice =
            await GetAllPaymentsByInvoice(payment.InvoiceNumber, cancellationToken);
        if (paymentsByInvoice.Any(p => p.IsSuccessful))
        {
            throw new DomainException($"Impossible to pay the same invoice {payment.InvoiceNumber} twice.");
        }
    }

    private static void SetInternalReference(PaymentAggregate? prevPayment, PaymentAggregate curPayment)
    {
        if (prevPayment != null)
        {
            bool needToRetryPrevPaymentIsFailure =
                prevPayment.InvoiceNumber != null && curPayment.InvoiceNumber != null &&
                prevPayment.InvoiceNumber == curPayment.InvoiceNumber && prevPayment.IsFailure;
            if (needToRetryPrevPaymentIsFailure)
                curPayment.SetAttempt(prevPayment.Attempt + 1);
        }

        curPayment.SetInternalReference();
    }

    private async Task<PaymentAggregate?> GetPreauthPaymentByPolicyIdAndPayorId(string policyId, string payorId,
        CancellationToken cancellationToken)
    {
        IEnumerable<PaymentAggregate> payments =
            await _paymentRepository.FindAllByAsync(p => p.PolicyId == policyId && p.PayorId == payorId,
                cancellationToken);
        PaymentAggregate? payment = payments.LastOrDefault(p => p is PreauthPaymentAggregate);
        return payment;
    }

    private async Task<PaymentAggregate?> GetPrevPaymentByPolicyIdAndPayorId(string policyId, string payorId,
        CancellationToken cancellationToken)
    {
        IEnumerable<PaymentAggregate> payments =
            await _paymentRepository.FindAllByAsync(p => p.PolicyId == policyId && p.PayorId == payorId,
                cancellationToken);
        PaymentAggregate? payment = payments.LastOrDefault(p => p is RecurringPaymentAggregate);
        return payment;
    }

    private async Task<IEnumerable<PaymentAggregate>> GetAllPaymentsByInvoice(string? invoiceNumber,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(invoiceNumber))
            return ArraySegment<PaymentAggregate>.Empty;
        return await _paymentRepository.FindAllByAsync(p => p.InvoiceNumber == invoiceNumber,
            cancellationToken);
    }

    public async Task<PaymentAggregate> FailPaymentAsync(string paymentId,
        CancellationToken cancellationToken = default)
    {
        using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = paymentId }))
        {
            PaymentAggregate payment = await _paymentRepository.GetByIdAsync(paymentId, cancellationToken);
            try
            {
                IPaymentProviderService pspService = GetPaymentProviderService(payment.PaymentProvider);
                await pspService.FailPaymentAsync(payment, cancellationToken);
            }
            catch (Exception exception)
            {
                if (!payment.Status.Equals(PaymentStatus.Failed))
                    payment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, payment.Money, exception.Message);
            }
            finally
            {
                await _paymentRepository.UpdateAsync(payment, cancellationToken);
            }

            return payment;
        }
    }

    public async Task<RefundAggregate> RefundPaymentAsync(string paymentId, decimal amount, int decimalPrecision,
        CancellationToken cancellationToken = default)
    {
        using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = paymentId }))
        {
            PaymentAggregate payment = await _paymentRepository.GetByIdAsync(paymentId, cancellationToken);
            if (amount > payment.Money.PaymentAmount - payment.CalculateRefundedAmount())
                throw new DomainException("Refund Amount must be less or equal of transaction remaining amount.");

            var paymentRefund =
                new RefundAggregate(payment.Id, RefundStatus.Failed,
                    new PaymentMoney(payment.Money.PaymentCurrencyCode, payment.Money.PaymentCurrencyDesc, amount,
                        decimalPrecision), null, payment.ProviderPaymentId);
            paymentRefund = await _refundRepository.InsertAsync(paymentRefund, cancellationToken);
            try
            {
                IPaymentProviderService pspService = GetPaymentProviderService(payment.PaymentProvider);
                paymentRefund = await pspService.RefundAsync(payment, paymentRefund, cancellationToken);
                if (paymentRefund.IsSuccessful)
                {
                    PaymentStatusHistoryItem? oldPaymentStatusHistoryItem = payment.PaymentStatusHistoryItem;
                    PaymentStatus status =
                        payment.CalculateRefundedAmount() + paymentRefund.Money.PaymentAmount ==
                        payment.Money.PaymentAmount
                            ? PaymentStatus.Refunded
                            : PaymentStatus.PartiallyRefunded;

                    payment.AddPaymentStatusHistoryItem(status, paymentRefund.Money);

                    if (oldPaymentStatusHistoryItem?.Status is PaymentStatus.Refunded
                        or PaymentStatus.PartiallyRefunded)
                    {
                        if (oldPaymentStatusHistoryItem.RefundId != null)
                            payment.PaymentStatusHistoryItem?.SetRefundId(
                                oldPaymentStatusHistoryItem.RefundId.Value + 1);
                    }
                    else
                        payment.PaymentStatusHistoryItem?.SetRefundId(1);
                }

                _logger.LogInformation(
                    $"Payment transaction refund result={paymentRefund} for amount = {amount}, id = {payment.Id}");
            }
            catch (Exception)
            {
                _logger.LogError("Payment transaction refund failed due to timeout.");
                if (!paymentRefund.Status.Equals(RefundStatus.Failed)) paymentRefund.SetStatus(RefundStatus.Failed);
            }
            finally
            {
                await _paymentRepository.UpdateAsync(payment, cancellationToken);
                await _refundRepository.UpdateAsync(paymentRefund, cancellationToken);
            }

            return paymentRefund;
        }
    }

    public async Task<(string responseStr, HttpStatusCode httpStatusCode)> HandleWebhookAsync(string webhookBody,
        PaymentProvider provider, CancellationToken cancellationToken = default)
    {
        try
        {
            (
                string paymentId,
                string providerPaymentId,
                string externalReference,
                string internalReference,
                PaymentStatus paymentStatus,
                decimal? amount,
                string webhookAcknowledgeMessage,
                bool isFinalizationRequired,
                bool skip
            ) = await HandleWebhookByPspAsync(webhookBody, provider, cancellationToken);

            if (skip) return (string.Empty, HttpStatusCode.OK);

            PaymentAggregate? payment =
                await TryToFindPaymentAsync(paymentId, externalReference, internalReference, cancellationToken);
            if (payment == null) return (string.Empty, HttpStatusCode.BadRequest);

            using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = payment.Id }))
            {
                bool statusChanged = payment.Status != paymentStatus;
                if (!string.IsNullOrWhiteSpace(providerPaymentId) &&
                    string.IsNullOrWhiteSpace(payment.ProviderPaymentId))
                {
                    payment.AssignProviderTransaction(providerPaymentId);
                    if (!statusChanged)
                        await _paymentRepository.UpdateAsync(payment, cancellationToken);
                }

                if (!statusChanged) return (webhookAcknowledgeMessage, HttpStatusCode.OK);

                if (payment is PreauthPaymentAggregate preauthPaymentAggregate &&
                    paymentStatus == PaymentStatus.Succeeded)
                    preauthPaymentAggregate.SetPreauthStatus(PreauthPaymentStatus.ToBePaid);

                payment.AddPaymentStatusHistoryItem(paymentStatus,
                    amount != null
                        ? new PaymentMoney(payment.Money.PaymentCurrencyCode, payment.Money.PaymentCurrencyDesc,
                            amount.Value,
                            payment.Money.PaymentDecimalPrecision)
                        : payment.Money, webhookBody: webhookBody);

                await _paymentRepository.UpdateAsync(payment, cancellationToken);

                if (isFinalizationRequired)
                    await FinalizePreauthPaymentAsync(payment.Id,
                        payment.DynamicFields == null
                            ? null
                            : System.Text.Json.JsonSerializer.Deserialize<JsonElement>(payment.DynamicFields),
                        true,
                        cancellationToken);

                return (webhookAcknowledgeMessage, HttpStatusCode.OK);
            }
        }
        catch (UnauthorizedException)
        {
            return (string.Empty, HttpStatusCode.Unauthorized);
        }
        catch
        {
            return (string.Empty, HttpStatusCode.InternalServerError);
        }
    }

    public async Task<PaymentAggregate> RegisterPaymentAsync(RegisterPaymentCommand registerPaymentCommand,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Received RegisterPaymentCommand: {RegisterPaymentCommand}", registerPaymentCommand);

        ValidateRegisterPaymentCommand(registerPaymentCommand);

        PaymentAggregate? payment;
        if (registerPaymentCommand.PaymentProvider != PaymentProvider.ExternalFile)
        {
            payment = await CreateNewPreauthPaymentAsync(registerPaymentCommand, cancellationToken);
            return await FinalizePreauthPaymentAsync(payment.Id, registerPaymentCommand.DynamicFields, false,
                cancellationToken);
        }

        PaymentType paymentType;
        switch (registerPaymentCommand.TransactionType)
        {
            case TransactionTypePremiumCollectedInitial:
                {
                    paymentType = PaymentType.Initial;
                    payment = await CreateNewPreauthPaymentAsync(registerPaymentCommand, cancellationToken);
                    break;
                }
            case TransactionTypePremiumCollectedEndorsement:
                {
                    paymentType = PaymentType.Receipt;
                    payment = await CreateNewPreauthPaymentAsync(registerPaymentCommand, cancellationToken);
                    break;
                }
            case TransactionTypePremiumCollectedRecurring:
                {
                    paymentType = PaymentType.Recurring;
                    payment = await CreateRecurringPaymentAsync(registerPaymentCommand, cancellationToken);
                    break;
                }
            case TransactionTypeRefund:
            case TransactionTypeRefundPaid:
                {
                    payment = await GetPaymentByUniqueConstraintAsync(registerPaymentCommand, cancellationToken) ??
                              await CreateNewPreauthPaymentAsync(registerPaymentCommand, cancellationToken);

                    return await CreateRefundAsync(payment, registerPaymentCommand, cancellationToken);
                }
            default:
                throw new DomainException(
                    $"Invalid transaction type. Should be '{TransactionTypePremiumCollectedInitial}' or '{TransactionTypePremiumCollectedRecurring}' or '{TransactionTypePremiumCollectedEndorsement}' or '{TransactionTypeRefund}'.");
        }

        SyncPaymentStatus(payment, registerPaymentCommand);
        SetPaymentMethodIfRequired(registerPaymentCommand, payment);

        if (payment.DomainEvents.Count > 0)
            payment.ClearDomainEvents();

        AddPaymentDomainEvent(payment, paymentType);

        await _paymentRepository.UpdateAsync(payment, cancellationToken);

        return payment;
    }

    private static void SetPaymentMethodIfRequired(RegisterPaymentCommand registerPaymentCommand,
        PaymentAggregate? payment)
    {
        if (!string.IsNullOrWhiteSpace(registerPaymentCommand.PaymentMethod) &&
            string.IsNullOrWhiteSpace(payment!.PaymentMethod))
            payment.SetPaymentMethod(registerPaymentCommand.PaymentMethod);
    }

    private void SyncPaymentStatus(PaymentAggregate payment, RegisterPaymentCommand command)
    {
        PaymentStatus updatedStatus = command.Status switch
        {
            TransactionSucceededStatus => PaymentStatus.Succeeded,
            TransactionFailedStatus => PaymentStatus.Failed,
            _ => throw new DomainException("Invalid payment status.")
        };

        if (payment.Status == updatedStatus)
            return;
        _logger.LogInformation(
            "Syncing payment status for PaymentId: {PaymentId}, OldStatus: {OldStatus}, NewStatus: {NewStatus}",
            payment.Id, payment.Status, updatedStatus);

        payment.AddPaymentStatusHistoryItem(updatedStatus, payment.Money,
            updatedStatus == PaymentStatus.Failed ? command.Error ?? "Failed by client." : null);
    }

    private async Task<PaymentAggregate> CreateRefundAsync(PaymentAggregate payment, RegisterPaymentCommand command,
        CancellationToken cancellationToken)
    {
        if (command.Amount > payment.Money.PaymentAmount - payment.CalculateRefundedAmount())
            throw new DomainException("Refund amount is greater than the remaining refundable amount.");

        RefundAggregate refund =
            await CreateRefundAggregateAsync(payment, command, cancellationToken);

        _logger.LogInformation("Refund status is {Status} for payment: {PaymentId}", payment.Id, refund.Status);

        SetPaymentMethodIfRequired(command, payment);

        if (!refund.IsSuccessful)
        {
            AddPaymentDomainEvent(payment, PaymentType.Refund, refund);
            await _paymentRepository.UpdateAsync(payment, cancellationToken);
            return payment;
        }

        payment.AddPaymentStatusHistoryItem(
            refund.Money.PaymentAmount == payment.Money.PaymentAmount
                ? PaymentStatus.Refunded
                : PaymentStatus.PartiallyRefunded, refund.Money);

        AddPaymentDomainEvent(payment, PaymentType.Refund, refund);

        await _paymentRepository.UpdateAsync(payment, cancellationToken);

        return payment;
    }

    private async Task<RefundAggregate> CreateRefundAggregateAsync(PaymentAggregate payment,
        RegisterPaymentCommand command,
        CancellationToken cancellationToken)
    {
        RefundStatus updatedStatus = command.Status switch
        {
            TransactionSucceededStatus => RefundStatus.Succeeded,
            TransactionFailedStatus => RefundStatus.Failed,
            _ => throw new DomainException("Invalid payment status.")
        };

        var refund = new RefundAggregate(
            payment.Id,
            updatedStatus,
            new PaymentMoney(payment.Money.PaymentCurrencyCode, payment.Money.PaymentCurrencyDesc, command.Amount, 2),
            command.Error,
            payment.ProviderPaymentId
        );

        return await _refundRepository.InsertAsync(refund, cancellationToken);
    }

    private async Task<RecurringPaymentAggregate> CreateRecurringPaymentAsync(
        RegisterPaymentCommand command, CancellationToken cancellationToken)
    {
        var recurringPayment = new RecurringPaymentAggregate(
            command.PaymentProvider,
            CreatePaymentMoney(command),
            command.PolicyId,
            command.InvoiceNumber,
            command.PayorId,
            command.EffectiveDate
        );

        recurringPayment.SetInternalReference();
        recurringPayment.SetDynamicFields(command.DynamicFields);

        ValidatePayment(recurringPayment);
        recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Prepared, recurringPayment.Money);

        await _paymentRepository.InsertAsync(recurringPayment, cancellationToken);
        return recurringPayment;
    }

    private static PaymentMoney CreatePaymentMoney(RegisterPaymentCommand command) =>
        new(
            CurrencyHelper.GetIsoCode(command.CurrencyCode),
            command.CurrencyCode,
            command.Amount,
            2
        );

    private async Task<PreauthPaymentAggregate> CreateNewPreauthPaymentAsync(RegisterPaymentCommand command,
        CancellationToken cancellationToken)
    {
        var preauthPayment = new PreauthPaymentAggregate(
            command.PaymentProvider,
            CreatePaymentMoney(command),
            command.PolicyId,
            command.InvoiceNumber,
            command.PayorId,
            command.EffectiveDate
        );

        ValidatePayment(preauthPayment);

        preauthPayment.SetInternalReference();
        preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Created, preauthPayment.Money);
        preauthPayment.SetDynamicFields(command.DynamicFields);

        if (command.PaymentProvider != PaymentProvider.ExternalFile)
        {
            await PopulatePspSettingsAsync(preauthPayment, cancellationToken);
        }

        await _paymentRepository.InsertAsync(preauthPayment, cancellationToken);
        _logger.LogInformation("Created new PreauthPaymentAggregate for policy: {PolicyId} and payorId: {PayorId}",
            command.PolicyId, command.PayorId);

        return preauthPayment;
    }

    private async Task<PaymentAggregate?> GetPaymentByUniqueConstraintAsync(RegisterPaymentCommand command,
        CancellationToken cancellationToken)
    {
        PaymentAggregate? payment =
            await GetPreauthPaymentByPolicyIdAndPayorId(command.PolicyId, command.PayorId, cancellationToken);
        if (payment != null && string.IsNullOrWhiteSpace(command.InvoiceNumber))
            return payment.InvoiceNumber == command.InvoiceNumber ? payment : null;

        return payment;
    }

    private static void ValidateRegisterPaymentCommand(RegisterPaymentCommand command)
    {
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(command.PayorId, nameof(command.PayorId));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(command.PolicyId, nameof(command.PolicyId));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(command.TransactionType, nameof(command.TransactionType));
        GuardClause.IsZeroOrNegative(command.Amount, nameof(command.Amount));
    }

    private async Task<PaymentAggregate?> TryToFindPaymentAsync(string paymentId,
        string externalReference, string internalReference, CancellationToken cancellationToken)
    {
        PaymentAggregate? payment = null;
        if (!string.IsNullOrWhiteSpace(paymentId))
            payment = await _paymentRepository.GetByIdAsync(paymentId, cancellationToken);
        else if (!string.IsNullOrWhiteSpace(externalReference))
            payment = (await _paymentRepository.FindAllByAsync(
                    p => p.ExternalReference != null && p.ExternalReference == externalReference,
                    cancellationToken))
                .FirstOrDefault();
        else if (!string.IsNullOrWhiteSpace(internalReference))
            payment = (await _paymentRepository.FindAllByAsync(
                    p => p.InternalReference == internalReference,
                    cancellationToken))
                .FirstOrDefault();

        return payment;
    }

    private async Task<PreauthPaymentAggregate> PreparePaymentAsync(PreauthPaymentAggregate preauthPayment,
        IPaymentProviderService pspService, CancellationToken cancellationToken)
    {
        preauthPayment = await pspService.PreparePaymentAsync(preauthPayment, cancellationToken);
        preauthPayment.SetPreauthStatus(PreauthPaymentStatus.ToBePaid);
        switch (preauthPayment.Status)
        {
            case PaymentStatus.Prepared:
            case PaymentStatus.Failed:
                await _paymentRepository.UpdateAsync(preauthPayment, cancellationToken);
                break;
        }

        return preauthPayment;
    }

    private async
        Task<(string paymentId, string providerPaymentId, string externalReference, string internalReference,
            PaymentStatus paymentStatus, decimal
            ? amount, string
            webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip)> HandleWebhookByPspAsync(
            string webhookBody,
            PaymentProvider provider,
            CancellationToken cancellationToken = default)
    {
        IPaymentProviderService pspService = GetPaymentProviderService(provider);
        IPspSettingsProvider pspSettingsProvider = GetPspSettingsProvider(provider);
        PspSettingsAggregate? pspSettings = await pspSettingsProvider.GetPspSettingsAsync(cancellationToken);

        return await pspService.HandleWebhookAsync(webhookBody, pspSettings, cancellationToken);
    }

    private async Task PopulatePspSettingsAsync(PaymentAggregate payment, CancellationToken cancellationToken)
    {
        PspSettingsAggregate? pspSettings = await GetPspSettingsProvider(payment.PaymentProvider)
            .GetPspSettingsAsync(cancellationToken);

        payment.SetPspSettings(JsonConvert.SerializeObject(pspSettings, Formatting.Indented));
    }

    private IPspSettingsProvider GetPspSettingsProvider(PaymentProvider type)
    {
        IPspSettingsProvider? provider = _pspSettingsProviders.SingleOrDefault(p => p.Type == type);
        if (provider != null) return provider;

        _logger.LogWarning($"Trying to use unsupported psp settings provider {type}");
        throw new NotImplementedException("Psp settings provider not supported.");
    }

    private static void ValidatePayment(PaymentAggregate payment)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(payment.Money, nameof(payment.Money));
        if (!IsUpdatePreauthPayment(payment))
            GuardClause.IsZeroOrNegative(payment.Money.PaymentAmount, nameof(payment.Money.PaymentAmount));

        GuardClause.IsZeroOrNegative(payment.Money.PaymentDecimalPrecision,
            nameof(payment.Money.PaymentDecimalPrecision));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(payment.Money.PaymentCurrencyCode,
            nameof(payment.Money.PaymentCurrencyCode));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(payment.Money.PaymentCurrencyDesc,
            nameof(payment.Money.PaymentCurrencyDesc));
    }

    private static bool IsUpdatePreauthPayment(PaymentAggregate payment) =>
        payment is PreauthPaymentAggregate { IsUpdate: true };

    private IPaymentProviderService GetPaymentProviderService(PaymentProvider paymentProvider)
    {
        IPaymentProviderService? paymentProviderService =
            _paymentProviderServices.FirstOrDefault(service => service.Type.Equals(paymentProvider));
        if (paymentProviderService != null) return paymentProviderService;

        _logger.LogWarning($"Trying to use unsupported payment provider {paymentProvider}");
        throw new NotImplementedException("Payment provider not supported");
    }

    private static void AddPaymentDomainEvent(PaymentAggregate payment, PaymentType paymentType,
        RefundAggregate? refund = null)
    {
        if (payment is { IsSuccessful: false, IsFailure: false }) return;

        PSPBearerPseudoCC pseudoCc = payment.InitialBearer as PSPBearerPseudoCC ?? new PSPBearerPseudoCC();
        Money money = CreateMoney(payment, refund);
        CreditCard creditCard = CreateCreditCard(pseudoCc);
        PaymentProviderInfo paymentProviderInfo = CreatePaymentProviderInfo(payment);
        PaymentBankInfo? paymentBankInfo = CreatePaymentBankInfo(payment);

        if (payment.IsSuccessful)
        {
            if (refund is { IsSuccessful: false })
            {
                var failedEvent = new PaymentFailedDomainEvent
                {
                    PolicyId = payment.PolicyId,
                    InvoiceNumber = payment.InvoiceNumber,
                    PayorId = payment.PayorId,
                    Status = refund.Status.ToString(),
                    EffectiveDate = GetEffectiveDate(payment),
                    Money = money,
                    CreditCard = creditCard,
                    PaymentProviderInfo = paymentProviderInfo,
                    FailureMessage = refund.ErrorDetails ?? "Failed by client.",
                    Type = paymentType.ToString(),
                    ReferenceId = payment.Id,
                    PaymentMethod = payment.PaymentMethod
                };

                if (payment.DomainEvents.Count > 0) payment.ClearDomainEvents();

                payment.AddDomainEvent(failedEvent);
            }
            else
            {
                var succeededEvent = new PaymentSucceededDomainEvent
                {
                    PolicyId = payment.PolicyId,
                    InvoiceNumber = payment.InvoiceNumber,
                    PayorId = payment.PayorId,
                    Status = refund?.Status.ToString() ?? payment.Status.ToString() ?? string.Empty,
                    EffectiveDate = GetEffectiveDate(payment),
                    Money = money,
                    CreditCard = creditCard,
                    PaymentProviderInfo = paymentProviderInfo,
                    Type = paymentType.ToString(),
                    ReferenceId = payment.Id,
                    PaymentMethod = payment.PaymentMethod,
                    PaymentBankInfo = paymentBankInfo
                };

                if (payment.DomainEvents.Count > 0) payment.ClearDomainEvents();

                payment.AddDomainEvent(succeededEvent);
            }
        }
        else if (payment.IsFailure)
        {
            var failedEvent = new PaymentFailedDomainEvent
            {
                PolicyId = payment.PolicyId,
                InvoiceNumber = payment.InvoiceNumber,
                PayorId = payment.PayorId,
                Status = payment.Status.ToString() ?? string.Empty,
                EffectiveDate = GetEffectiveDate(payment),
                Money = money,
                CreditCard = creditCard,
                PaymentProviderInfo = paymentProviderInfo,
                FailureMessage = payment.PaymentStatusHistoryItem?.Error ?? string.Empty,
                Type = paymentType.ToString(),
                ReferenceId = payment.Id,
                PaymentMethod = payment.PaymentMethod
            };

            if (payment.DomainEvents.Count > 0) payment.ClearDomainEvents();

            payment.AddDomainEvent(failedEvent);
        }
    }

    private static void AddCreditCardUpdateDomainEvent(PaymentAggregate payment)
    {
        if (payment is { IsSuccessful: false, IsFailure: false })
            return;

        PSPBearerPseudoCC pseudoCc = payment.InitialBearer as PSPBearerPseudoCC ?? new PSPBearerPseudoCC();
        CreditCard creditCard = CreateCreditCard(pseudoCc);
        IDomainEvent? domainEvent = null;
        if (payment.IsSuccessful)
            domainEvent = new CreditCardUpdateSucceededDomainEvent
            {
                PolicyId = payment.PolicyId,
                PayorId = payment.PayorId,
                EffectiveDate = GetEffectiveDate(payment),
                CreditCard = creditCard,
                PaymentMethod = payment.PaymentMethod
            };
        else if (payment.IsFailure)
            domainEvent = new CreditCardUpdateFailedDomainEvent
            {
                PolicyId = payment.PolicyId,
                PayorId = payment.PayorId,
                EffectiveDate = GetEffectiveDate(payment),
                CreditCard = creditCard,
                FailureMessage = payment.PaymentStatusHistoryItem?.Error ?? string.Empty,
                PaymentMethod = payment.PaymentMethod
            };

        if (domainEvent != null)
        {
            if (payment.DomainEvents.Count > 0)
                payment.ClearDomainEvents();
            payment.AddDomainEvent(domainEvent);
        }
    }

    private static DateTime GetEffectiveDate(PaymentAggregate payment) =>
        payment.EffectiveDate ?? payment.PaymentStatusHistoryItem?.CreatedAtDateUtc ?? DateTime.UtcNow;

    private static Money CreateMoney(PaymentAggregate payment, RefundAggregate? refund) =>
        new()
        {
            CurrencyCode = refund?.Money.PaymentCurrencyCode ?? payment.Money.PaymentCurrencyCode,
            CurrencyDesc = refund?.Money.PaymentCurrencyDesc ?? payment.Money.PaymentCurrencyDesc,
            Amount = refund?.Money.PaymentAmount ?? payment.Money.PaymentAmount,
            DecimalPrecision = refund?.Money.PaymentDecimalPrecision ?? payment.Money.PaymentDecimalPrecision
        };

    private static CreditCard CreateCreditCard(PSPBearerPseudoCC pseudoCc) =>
        new()
        {
            Holder = pseudoCc.Holder,
            CardNumber = pseudoCc.TruncatedCardPan,
            CardType = pseudoCc.CardType,
            Country = pseudoCc.Country,
            ExpiryYear = pseudoCc.ExpiryYear,
            ExpiryMonth = pseudoCc.ExpiryMonth
        };

    private static PaymentProviderInfo CreatePaymentProviderInfo(PaymentAggregate payment)
    {
        var paymentProviderInfo = new PaymentProviderInfo
        {
            ExternalPaymentReference = payment.ExternalReference,
            InternalPaymentReference = payment.Id,
            PaymentProvider = payment.PaymentProvider.ToString()
        };

        if (payment is PreauthPaymentAggregate preauthPayment)
            paymentProviderInfo.InitializationToken = preauthPayment.InitializationToken;

        return paymentProviderInfo;
    }

    private static PaymentBankInfo? CreatePaymentBankInfo(PaymentAggregate payment)
    {
        if (string.IsNullOrWhiteSpace(payment.DynamicFields))
        {
            return null;
        }

        try
        {
            return System.Text.Json.JsonSerializer.Deserialize<PaymentBankInfo>(payment.DynamicFields);
        }
        catch (System.Text.Json.JsonException)
        {
            return null;
        }
    }

    private static void ValidateCancellationStatus(PaymentStatus cancellationStatus)
    {
        // Define valid cancellation statuses
        var validCancellationStatuses = new[]
        {
            PaymentStatus.Canceled,
            PaymentStatus.Expired,
            PaymentStatus.Failed
        };

        if (!validCancellationStatuses.Contains(cancellationStatus))
        {
            throw new DomainException($"Invalid cancellation status: {cancellationStatus}. Valid statuses are: {string.Join(", ", validCancellationStatuses)}");
        }
    }

    private static PreauthPaymentStatus GetPreauthStatusForCancellation(PaymentStatus cancellationStatus)
    {
        return cancellationStatus switch
        {
            PaymentStatus.Canceled => PreauthPaymentStatus.Cancelled,
            PaymentStatus.Expired => PreauthPaymentStatus.Cancelled, // Map expired to cancelled preauth status
            PaymentStatus.Failed => PreauthPaymentStatus.CancelFailed,
            _ => PreauthPaymentStatus.CancelFailed
        };
    }

    private async Task<string?> GetTenantConfigRedirectUrlAsync(string clientKey, CancellationToken cancellationToken = default)
    {
        if (!_tenantProvider.TryGetCurrent(out TenantId? tenantId))
            return null;

        TenantSettings tenantSettings = await _authService.GetTenantSettingsAsync(tenantId.Value, cancellationToken);

        string? memberPortalOrigin = tenantSettings?.Hosts?.FirstOrDefault(h => h.Contains(clientKey));
        if (string.IsNullOrWhiteSpace(memberPortalOrigin))
            return null;

        memberPortalOrigin = EnsureUrlHasSchema(memberPortalOrigin);
        return memberPortalOrigin;
    }

    private static string? ExtractClientKeyFromDynamicFields(JsonElement? dynamicFields)
    {
        if (dynamicFields == null)
            return null;

        try
        {
            if (dynamicFields.Value.TryGetProperty("clientKey", out JsonElement clientKeyElement))
            {
                return clientKeyElement.GetString();
            }
        }
        catch (System.Text.Json.JsonException)
        {
            // Invalid JSON, return null
        }

        return null;
    }
}