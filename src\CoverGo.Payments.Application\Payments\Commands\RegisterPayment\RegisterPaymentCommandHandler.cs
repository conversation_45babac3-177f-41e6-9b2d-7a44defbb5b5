﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.RegisterPayment
{
    public class RegisterPaymentCommandHandler(IMapper mapper, IPaymentService paymentService)
        : ICommandHandler<RegisterPaymentCommand, PaymentDto>
    {
        /// <param name="registerPaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentDto> Handle(RegisterPaymentCommand registerPaymentCommand,
            CancellationToken cancellationToken)
        {
            PaymentAggregate payment =
                await paymentService.RegisterPaymentAsync(registerPaymentCommand, cancellationToken);
            return mapper.Map<PaymentDto>(payment);
        }
    }
}