using System.Text.RegularExpressions;
using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using CoverGo.Payments.Application.Common;
using CoverGo.Payments.Application.Common.Handlers;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Domain.Payment;
using MongoDB.Bson;
using MongoDB.Driver;

namespace CoverGo.Payments.Application.Payments.Queries.MemberPaymentsQuery;

public class MemberPaymentsQueryHandler(
    IMapper mapper,
    IMongoCollection<PaymentAggregate> mongoCollection)
    : AggregatesQueryHandler<PaymentAggregate, MemberPaymentsQuery, MemberPaymentsWhere>(mapper, mongoCollection),
        IQueryHandler<MemberPaymentsQuery, PagedResult<PaymentAggregate>>
{
    protected override FilterDefinition<PaymentAggregate> GetFilter(MemberPaymentsWhere? filter)
    {
        FilterDefinition<PaymentAggregate>? mongoFilter = SBuilder.Eq("_t", nameof(PreauthPaymentAggregate));

        if (filter is null)
            return mongoFilter;

        if (filter.And is not null)
            return filter.And.GetAndFilter(GetFilter);

        if (filter.Or is not null)
            return filter.Or.GetOrFilter(GetFilter);

        if (filter.TransactionType?.In?.Any() == true)
            mongoFilter &= SBuilder.In(o => o.Type, filter.TransactionType?.In);

        if (filter.PaymentSource != null && filter.PaymentSource?.In?.Length != 0)
            mongoFilter &= SBuilder.In(o => o.PaymentProvider, filter.PaymentSource?.In);

        if (filter.Status != null && filter.Status?.In?.Any() == true)
            mongoFilter &= SBuilder.In(o => o.Status, filter.Status?.In);

        if (filter.EffectiveDate?.Gte.HasValue == true)
            mongoFilter &= SBuilder.Gte(o => o.EffectiveDate!.Value, filter.EffectiveDate.Gte.Value);

        if (filter.EffectiveDate?.Lt.HasValue == true)
            mongoFilter &= SBuilder.Lte(o => o.EffectiveDate!.Value, filter.EffectiveDate.Lt.Value);

        if (filter.InvoiceNumber != null)
        {
            if (!string.IsNullOrEmpty(filter.InvoiceNumber.Eq))
                mongoFilter &= SBuilder.Eq(o => o.InvoiceNumber, filter.InvoiceNumber.Eq);

            if (!string.IsNullOrEmpty(filter.InvoiceNumber.Contains))
                mongoFilter &= SBuilder.Regex(o => o.InvoiceNumber, new BsonRegularExpression(Regex.Escape(filter.InvoiceNumber.Contains), "i"));
        }

        if (filter.PolicyId != null)
        {
            if (!string.IsNullOrEmpty(filter.PolicyId.Eq))
                mongoFilter &= SBuilder.Eq(o => o.PolicyId, filter.PolicyId.Eq);

            if (!string.IsNullOrEmpty(filter.PolicyId.Contains))
                mongoFilter &= SBuilder.Regex(o => o.PolicyId, new BsonRegularExpression(Regex.Escape(filter.PolicyId.Contains), "i"));
        }

        if (filter.PaymentId != null)
        {
            if (!string.IsNullOrEmpty(filter.PaymentId.Eq))
                mongoFilter &= SBuilder.Eq(o => o.Id, filter.PaymentId.Eq);

            if (!string.IsNullOrEmpty(filter.PaymentId.Contains))
                mongoFilter &= SBuilder.Regex(o => o.Id, new BsonRegularExpression(Regex.Escape(filter.PaymentId.Contains), "i"));
        }

        if (filter.PayorId != null)
        {
            if (!string.IsNullOrEmpty(filter.PayorId.Eq))
                mongoFilter &= SBuilder.Eq(o => o.PayorId, filter.PayorId.Eq);
            if (!string.IsNullOrEmpty(filter.PayorId.Contains))
                mongoFilter &= SBuilder.Regex(o => o.PayorId, new BsonRegularExpression(Regex.Escape(filter.PayorId.Contains), "i"));
        }

        // Note: PaymentToken filtering is handled at the application level in the Handle method
        // to avoid MongoDB deserialization issues with nested field queries

        return mongoFilter;
    }

    protected override IEnumerable<SortDefinition<PaymentAggregate>> GetSorts(MemberPaymentsQuery query) =>
        [Builders<PaymentAggregate>.Sort.Descending(o => o.EffectiveDate)];

    public async Task<PagedResult<PaymentAggregate>> Handle(MemberPaymentsQuery request, CancellationToken cancellationToken)
    {
        var result = await Handle<PaymentAggregate>(request, cancellationToken);

        // Apply PaymentToken filter at application level to avoid MongoDB deserialization issues
        if (request.Where?.PaymentToken?.In?.Any() == true)
        {
            var tokenStatuses = request.Where.PaymentToken.In.ToList();
            var hasTokenized = tokenStatuses.Contains(PaymentTokenStatus.TOKENIZED);
            var hasNoTokenized = tokenStatuses.Contains(PaymentTokenStatus.NO_TOKENIZED);

            if (hasTokenized && hasNoTokenized)
            {
                // Both requested - return all results as-is
                return result;
            }

            var filteredItems = result.Items.Where(payment =>
            {
                var hasToken = payment.InitialBearer != null &&
                              payment.InitialBearer is PSPBearerPseudoCC bearer &&
                              !string.IsNullOrEmpty(bearer.Token);

                if (hasTokenized && !hasNoTokenized)
                    return hasToken; // Only tokenized
                else if (hasNoTokenized && !hasTokenized)
                    return !hasToken; // Only non-tokenized
                else
                    return true; // Both (shouldn't reach here due to check above)
            }).ToList();

            return new PagedResult<PaymentAggregate>
            {
                Items = filteredItems,
                TotalCount = filteredItems.Count // Note: This won't be accurate for pagination, but it's a limitation of post-filtering
            };
        }

        return result;
    }
}