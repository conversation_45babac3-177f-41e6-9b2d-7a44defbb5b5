using System.Text.RegularExpressions;
using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using CoverGo.Payments.Application.Common;
using CoverGo.Payments.Application.Common.Handlers;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Domain.Payment;
using MongoDB.Bson;
using MongoDB.Driver;

namespace CoverGo.Payments.Application.Payments.Queries.PaymentsQuery;

public class PaymentsQueryHandler(
    IMapper mapper,
    IMongoCollection<PaymentAggregate> mongoCollection)
    : AggregatesQueryHandler<PaymentAggregate, PaymentsQuery, PaymentsWhere>(mapper, mongoCollection),
        IQueryHandler<PaymentsQuery, PagedResult<PaymentAggregate>>
{
    protected override FilterDefinition<PaymentAggregate> GetFilter(PaymentsWhere? filter)
    {
        FilterDefinition<PaymentAggregate>? mongoFilter = SBuilder.Empty;

        if (filter is null) 
            return mongoFilter;

        if (filter.And is not null) 
            return filter.And.GetAndFilter(GetFilter);

        if (filter.Or is not null) 
            return filter.Or.GetOrFilter(GetFilter);

        if (filter.TransactionType?.In?.Any() == true) 
            mongoFilter &= SBuilder.In(o => o.Type, filter.TransactionType?.In);

        if (filter.PaymentSource != null && filter.PaymentSource?.In?.Length != 0) 
            mongoFilter &= SBuilder.In(o => o.PaymentProvider, filter.PaymentSource?.In);

        if (filter.Status != null && filter.Status?.In?.Any() == true) 
            mongoFilter &= SBuilder.In(o => o.Status, filter.Status?.In);

        if (filter.EffectiveDate?.Gte.HasValue == true)
            mongoFilter &= SBuilder.Gte(o => o.EffectiveDate!.Value, filter.EffectiveDate.Gte.Value);

        if (filter.EffectiveDate?.Lt.HasValue == true)
            mongoFilter &= SBuilder.Lte(o => o.EffectiveDate!.Value, filter.EffectiveDate.Lt.Value);
        
        if (filter.InvoiceNumber != null)
        {
            if (!string.IsNullOrEmpty(filter.InvoiceNumber.Eq)) 
                mongoFilter &= SBuilder.Eq(o => o.InvoiceNumber, filter.InvoiceNumber.Eq);

            if (!string.IsNullOrEmpty(filter.InvoiceNumber.Contains)) 
                mongoFilter &= SBuilder.Regex(o => o.InvoiceNumber, new BsonRegularExpression(Regex.Escape(filter.InvoiceNumber.Contains), "i"));
        }

        if (filter.PaymentToken?.In?.Any() == true)
        {
            var tokenFilters = new List<FilterDefinition<PaymentAggregate>>();

            foreach (var tokenStatus in filter.PaymentToken.In)
            {
                switch (tokenStatus)
                {
                    case PaymentTokenStatus.TOKENIZED:
                        // Payment has InitialBearer with a non-empty Token
                        tokenFilters.Add(
                            SBuilder.Regex("InitialBearer.Token", new BsonRegularExpression("^.+$"))
                        );
                        break;

                    case PaymentTokenStatus.NO_TOKENIZED:
                        // Payment has no InitialBearer or Token is null/empty
                        tokenFilters.Add(
                            SBuilder.Or(
                                SBuilder.Eq("InitialBearer", BsonNull.Value),
                                SBuilder.Eq("InitialBearer.Token", BsonNull.Value),
                                SBuilder.Eq("InitialBearer.Token", "")
                            )
                        );
                        break;
                }
            }

            if (tokenFilters.Count == 1)
            {
                mongoFilter &= tokenFilters[0];
            }
            else if (tokenFilters.Count > 1)
            {
                mongoFilter &= SBuilder.Or(tokenFilters);
            }
        }
        
        return mongoFilter;
    }
    
    protected override IEnumerable<SortDefinition<PaymentAggregate>> GetSorts(PaymentsQuery query) =>
        [Builders<PaymentAggregate>.Sort.Descending(o => o.EffectiveDate)];

    public Task<PagedResult<PaymentAggregate>> Handle(PaymentsQuery request, CancellationToken cancellationToken)
        => Handle<PaymentAggregate>(request, cancellationToken);
}