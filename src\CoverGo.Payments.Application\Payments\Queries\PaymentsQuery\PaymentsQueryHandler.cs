using System.Text.RegularExpressions;
using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using CoverGo.Payments.Application.Common;
using CoverGo.Payments.Application.Common.Handlers;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Domain.Payment;
using MongoDB.Bson;
using MongoDB.Driver;

namespace CoverGo.Payments.Application.Payments.Queries.PaymentsQuery;

public class PaymentsQueryHandler(
    IMapper mapper,
    IMongoCollection<PaymentAggregate> mongoCollection)
    : AggregatesQueryHandler<PaymentAggregate, PaymentsQuery, PaymentsWhere>(mapper, mongoCollection),
        IQueryHandler<PaymentsQuery, PagedResult<PaymentAggregate>>
{
    protected override FilterDefinition<PaymentAggregate> GetFilter(PaymentsWhere? filter)
    {
        FilterDefinition<PaymentAggregate>? mongoFilter = SBuilder.Empty;

        if (filter is null) 
            return mongoFilter;

        if (filter.And is not null) 
            return filter.And.GetAndFilter(GetFilter);

        if (filter.Or is not null) 
            return filter.Or.GetOrFilter(GetFilter);

        if (filter.TransactionType?.In?.Any() == true) 
            mongoFilter &= SBuilder.In(o => o.Type, filter.TransactionType?.In);

        if (filter.PaymentSource != null && filter.PaymentSource?.In?.Length != 0) 
            mongoFilter &= SBuilder.In(o => o.PaymentProvider, filter.PaymentSource?.In);

        if (filter.Status != null && filter.Status?.In?.Any() == true) 
            mongoFilter &= SBuilder.In(o => o.Status, filter.Status?.In);

        if (filter.EffectiveDate?.Gte.HasValue == true)
            mongoFilter &= SBuilder.Gte(o => o.EffectiveDate!.Value, filter.EffectiveDate.Gte.Value);

        if (filter.EffectiveDate?.Lt.HasValue == true)
            mongoFilter &= SBuilder.Lte(o => o.EffectiveDate!.Value, filter.EffectiveDate.Lt.Value);
        
        if (filter.InvoiceNumber != null)
        {
            if (!string.IsNullOrEmpty(filter.InvoiceNumber.Eq)) 
                mongoFilter &= SBuilder.Eq(o => o.InvoiceNumber, filter.InvoiceNumber.Eq);

            if (!string.IsNullOrEmpty(filter.InvoiceNumber.Contains)) 
                mongoFilter &= SBuilder.Regex(o => o.InvoiceNumber, new BsonRegularExpression(Regex.Escape(filter.InvoiceNumber.Contains), "i"));
        }

        // Note: PaymentToken filtering is handled at the application level in the Handle method
        // to avoid MongoDB deserialization issues with nested field queries
        
        return mongoFilter;
    }
    
    protected override IEnumerable<SortDefinition<PaymentAggregate>> GetSorts(PaymentsQuery query) =>
        [Builders<PaymentAggregate>.Sort.Descending(o => o.EffectiveDate)];

    public async Task<PagedResult<PaymentAggregate>> Handle(PaymentsQuery request, CancellationToken cancellationToken)
    {
        var result = await Handle<PaymentAggregate>(request, cancellationToken);

        // Apply PaymentToken filter at application level to avoid MongoDB deserialization issues
        if (request.Where?.PaymentToken?.In?.Any() == true)
        {
            var tokenStatuses = request.Where.PaymentToken.In.ToList();
            var hasTokenized = tokenStatuses.Contains(PaymentTokenStatus.TOKENIZED);
            var hasNoTokenized = tokenStatuses.Contains(PaymentTokenStatus.NO_TOKENIZED);

            if (hasTokenized && hasNoTokenized)
            {
                // Both requested - return all results as-is
                return result;
            }

            var filteredItems = result.Items.Where(payment =>
            {
                var hasToken = payment.InitialBearer != null &&
                              payment.InitialBearer is PSPBearerPseudoCC bearer &&
                              !string.IsNullOrEmpty(bearer.Token);

                if (hasTokenized && !hasNoTokenized)
                    return hasToken; // Only tokenized
                else if (hasNoTokenized && !hasTokenized)
                    return !hasToken; // Only non-tokenized
                else
                    return true; // Both (shouldn't reach here due to check above)
            }).ToList();

            return new PagedResult<PaymentAggregate>
            {
                Items = filteredItems,
                TotalCount = filteredItems.Count // Note: This won't be accurate for pagination, but it's a limitation of post-filtering
            };
        }

        return result;
    }
}