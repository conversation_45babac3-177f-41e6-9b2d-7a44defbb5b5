﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.CancelPayment
{
    public class CancelPaymentCommandHandler(IMapper mapper, IPaymentService paymentService)
        : ICommandHandler<CancelPaymentCommand, PaymentDto>
    {
        /// <param name="cancelPaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentDto> Handle(CancelPaymentCommand cancelPaymentCommand,
            CancellationToken cancellationToken)
        {
            PaymentAggregate payment = await paymentService.CancelPreauthPaymentAsync(
                cancelPaymentCommand.PaymentId, 
                cancelPaymentCommand.CancellationStatus ?? PaymentStatus.Canceled,
                cancelPaymentCommand.CancellationReason,
                cancellationToken);
            return mapper.Map<PaymentDto>(payment);
        }
    }
}
