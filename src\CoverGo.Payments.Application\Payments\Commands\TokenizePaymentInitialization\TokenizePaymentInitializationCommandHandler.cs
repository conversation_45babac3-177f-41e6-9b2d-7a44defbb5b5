﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.TokenizePaymentInitialization
{
    public class TokenizePaymentInitializationCommandHandler(IMapper mapper, IPaymentService paymentService)
        : ICommandHandler<TokenizePaymentInitializationCommand, TokenizedPaymentInitializationDto>
    {
        public async Task<TokenizedPaymentInitializationDto> Handle(TokenizePaymentInitializationCommand command,
            CancellationToken cancellationToken)
        {
            TokenizedPaymentInitializationAggregate aggregate = new(
                command.PaymentProvider,
                command.Amount,
                command.CurrencyCode,
                command.CurrencyDesc,
                command.DecimalPrecision,
                command.DynamicFields,
                command.PublicFields,
                command.PolicyId,
                command.InvoiceNumber,
                command.PayorId,
                command.IsCardUpdate ?? false
            );

            TokenizedPaymentInitialization tokenizedPaymentInitialization =
                await paymentService.TokenizePaymentInitializationAsync(aggregate, cancellationToken);

            TokenizedPaymentInitializationDto? result =
                mapper.Map<TokenizedPaymentInitializationDto>(tokenizedPaymentInitialization);

            return result;
        }
    }
}